{% extends "base.html" %} {% block title %}Inventory Management{% endblock %} {%
block extra_css %}
<link
  rel="stylesheet"
  href="{{ url_for('static', filename='css/inventory_modal.css') }}"
/>

<style></style>
{% endblock %} {% block content %}
<div class="container mx-auto px-4 py-6">
  <!-- Header -->
  <div class="mb-6">
    <h1 class="text-2xl font-bold text-gray-800 dark:text-white">
      Inventory Management
    </h1>
    <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
      Manage your wafer inventory and track locations
    </p>
  </div>

  <!-- Search Filters -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
    <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
      <!-- Basic Info Fields -->
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Wafer ID</label
        >
        <input
          type="text"
          id="wafer-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Wafer ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >LGT Lot ID</label
        >
        <input
          type="text"
          id="lot-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter LGT Lot ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Xfab Lot ID</label
        >
        <input
          type="text"
          id="xfab-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Xfab Lot ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Mask Set ID</label
        >
        <input
          type="text"
          id="mask-set-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Mask Set ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Module Name</label
        >
        <input
          type="text"
          id="module-name"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Module Name"
        />
      </div>

      <!-- Location Fields -->
      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Cassette ID</label
        >
        <input
          type="text"
          id="cassette-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Cassette ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Slot ID</label
        >
        <input
          type="number"
          id="slot-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          placeholder="Enter Slot ID"
        />
      </div>

      <div>
        <label
          class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2"
          >Location</label
        >
        <select
          id="location-id"
          class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
          title="Location"
        >
          <option value="">All Locations</option>
          <!-- Other options will be populated dynamically -->
        </select>
      </div>

      <!-- Date Range Fields -->
      <div
        class="col-span-full lg:col-span-4 grid grid-cols-1 md:grid-cols-2 gap-4"
      >
        <!-- Arrival Period -->
        <div class="space-y-2">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Arrival Period</label
          >
          <div class="flex items-center space-x-2">
            <div class="date-picker-wrapper flex-1">
              <input
                type="date"
                id="arrived-at-from"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                title="Arrived At"
              />
            </div>
            <span class="text-gray-500">to</span>
            <div class="date-picker-wrapper flex-1">
              <input
                type="date"
                id="arrived-at-to"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                title="Arrived At"
              />
            </div>
          </div>
        </div>

        <!-- Sent Period -->
        <div class="space-y-2">
          <label
            class="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >Sent Period</label
          >
          <div class="flex items-center space-x-2">
            <div class="date-picker-wrapper flex-1">
              <input
                type="date"
                id="sent-at-from"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                title="Sent At"
              />
            </div>
            <span class="text-gray-500">to</span>
            <div class="date-picker-wrapper flex-1">
              <input
                type="date"
                id="sent-at-to"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                title="Sent At"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Floating Action Buttons -->
  <div class="fixed bottom-8 right-8 flex flex-col gap-4">
    <button
      id="clear-form-btn"
      onclick="clearForm()"
      class="bg-gray-500 hover:bg-gray-600 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-105 group"
    >
      <i class="fas fa-eraser text-xl"></i>
      <span
        class="absolute right-full mr-3 bg-gray-800 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity"
      >
        Clear Filters
      </span>
    </button>
    <button
      onclick="searchInventory()"
      class="bg-blue-600 hover:bg-blue-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 hover:scale-105 group"
    >
      <i class="fas fa-search text-xl"></i>
      <span
        class="absolute right-full mr-3 bg-gray-800 text-white px-2 py-1 rounded text-sm opacity-0 group-hover:opacity-100 transition-opacity"
      >
        Search
      </span>
    </button>
  </div>

  <!-- Results Section -->
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
    <!-- Action Buttons -->
    <div
      class="p-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center"
    >
      <div class="text-sm text-gray-500 dark:text-gray-400">
        <span id="selected-count">0</span> items selected
      </div>
      <div class="flex space-x-3">
        <button
          onclick="syncWafersToInventory()"
          class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="sync"
        >
          <i class="fas fa-sync-alt mr-2"></i>Sync Wafers
        </button>
        <button
          onclick="addInventory()"
          class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="add"
        >
          <i class="fas fa-plus mr-2"></i>Add
        </button>
        <button
          onclick="modifyInventory()"
          class="bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="modify"
        >
          <i class="fas fa-edit mr-2"></i>Modify
        </button>
        <button
          onclick="deleteInventory()"
          class="bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
          data-permission="delete"
        >
          <i class="fas fa-trash mr-2"></i>Delete
        </button>
        <button
          onclick="exportToCSV()"
          class="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
        >
          <i class="fas fa-file-export mr-2"></i>Export CSV
        </button>
        <button
          onclick="forceRefreshInventoryData()"
          class="bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out"
        >
          <i class="fas fa-sync-alt mr-2"></i>Refresh Data
        </button>
      </div>
    </div>

    <!-- Table Headers -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="table-container custom-scrollbar">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <!-- Table Headers -->
          <thead class="sticky-header bg-gray-50 dark:bg-gray-700">
            <tr>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                <input
                  type="checkbox"
                  id="select-all"
                  class="rounded border-gray-300 text-blue-600 shadow-sm focus:ring"
                  title="Select All"
                />
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('wafer_id')"
              >
                WAFER ID
                <span id="sort-wafer_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('lot_id')"
              >
                LGT LOT ID
                <span id="sort-lot_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('xfab_id')"
              >
                XFAB LOT ID
                <span id="sort-xfab_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('mask_set_id')"
              >
                MASK SET ID
                <span id="sort-mask_set_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('module_name')"
              >
                MODULE NAME
                <span id="sort-module_name" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('cassette_id')"
              >
                CASSETTE ID
                <span id="sort-cassette_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('slot_id')"
              >
                SLOT ID
                <span id="sort-slot_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('location_id')"
              >
                LOCATION
                <span id="sort-location_id" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('arrived_at')"
              >
                ARRIVED AT
                <span id="sort-arrived_at" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider cursor-pointer"
                onclick="handleSort('sent_at')"
              >
                SENT AT
                <span id="sort-sent_at" class="sort-indicator"></span>
              </th>
              <th
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
              >
                HISTORY
              </th>
            </tr>
          </thead>
          <tbody
            class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700"
            id="inventory-table-body"
          >
            <!-- Table content will be dynamically populated -->
          </tbody>
        </table>
      </div>
    </div>
    <!-- Pagination -->
    <div
      class="mt-4 flex items-center justify-between px-4 py-3 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700"
    >
      <div class="flex items-center">
        <span class="text-sm text-gray-700 dark:text-gray-300">Show</span>
        <select
          id="page-size"
          class="mx-2 border-gray-300 rounded-md text-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600"
          title="Items per page"
        >
          <option value="10">10</option>
          <option value="25">25</option>
          <option value="50">50</option>
          <option value="100">100</option>
        </select>
        <span class="text-sm text-gray-700 dark:text-gray-300">entries</span>
      </div>

      <div class="flex items-center space-x-4">
        <div class="text-sm text-gray-700 dark:text-gray-300">
          Showing <span id="page-start">0</span> to
          <span id="page-end">0</span> of <span id="total-items">0</span>
          entries
        </div>
        <div class="flex space-x-2">
          <button
            id="prev-page"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          <button
            id="next-page"
            class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 dark:bg-gray-700 dark:border-gray-600 dark:text-gray-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Next
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script src="{{ url_for('static', filename='js/inventory_management.js') }}"></script>

<script src="{{ url_for('static', filename='js/debug.js') }}"></script>

{% endblock %}
