"""
Routes for inventory management.
"""

import json
import re  # noqa: F401
import traceback
from datetime import datetime, timezone
from decimal import Decimal  # noqa: F401
from typing import Dict, List, Tuple  # noqa: F401

import psycopg2.errors
from flask import (
    Blueprint,
    current_app,
    jsonify,
    request,
    session,
)

try:
    from flask_wtf.csrf import exempt
except ImportError:
    # Fallback for older versions of Flask-WTF
    try:
        from flask_wtf import exempt
    except ImportError:
        # If CSRF is not available, create a dummy decorator
        def exempt(f):
            return f


from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError

from core.auth.auth import is_authenticated, login_required, permission_required
from core.models.models import WaferInventoryModel

# Import helper functions
from database.db_config import get_db_cursor
from database.db_config import get_db_url as config_get_db_url
from database.db_helpers import require_database
from routes.helpers.inventory_change_tracking import track_wafer_change
from routes.helpers.inventory_metadata_helpers import update_metadata_fields
from routes.helpers.inventory_update_helpers import (
    get_wafer_inventory_values,
    validate_inventory_update,
)

inventory_bp = Blueprint("inventory", __name__)


def get_db_url():
    """Get database URL string from app configuration or environment variables"""
    try:
        # Try to get from Flask app config
        if current_app and "DB_CONNECTION" in current_app.config:
            return current_app.config["DB_CONNECTION"]

        # If not found, use the database.db_config version
        return config_get_db_url()
    except Exception as e:
        current_app.logger.error(f"Error getting database URL: {str(e)}")
        # Fallback to direct DB URL construction if all else fails
        from database.db_config import DATABASE_URL

        return DATABASE_URL


def get_engine():
    """Get SQLAlchemy engine using the database URL"""
    return create_engine(get_db_url())


@inventory_bp.route("/api/inventory/add", methods=["POST"])
@require_database()
@login_required
@permission_required("create")
def add_inventory():
    # Force response content type to be JSON
    response_headers = {"Content-Type": "application/json"}

    try:
        data = request.get_json()
        current_app.logger.info(f"Add request data: {data}")

        # Extract data from request body
        wafer_id_list = data.get("wafer_ids", [])
        slot_id_list = data.get("slot_ids", [])
        cassette_id = data.get("cassette_id", "")
        location_id = data.get("location_id")
        arrived_at = data.get("arrived_at")
        sent_at = data.get("sent_at")
        module_name = data.get("module_name", "")
        lot_id = data.get("lot_id", "")
        xfab_fr_lot_id = data.get("xfab_id", "")
        mask_set_id = data.get("mask_set_id", "")

        # Validation
        if not wafer_id_list or len(wafer_id_list) != len(slot_id_list):
            return (
                jsonify(
                    {"success": False, "message": "Invalid wafer or slot IDs provided"}
                ),
                400,
                response_headers,
            )

        engine = create_engine(get_db_url())
        with engine.connect() as conn:
            # Start an explicit transaction
            trans = conn.begin()
            try:
                # 1. Vérifier l'existence des wafer_id dans wafer_inventory
                existing_wafers = conn.execute(
                    text(
                        "SELECT wafer_id FROM wafer_inventory "
                        "WHERE wafer_id = ANY(:wafer_ids)"
                    ),
                    {"wafer_ids": wafer_id_list},
                ).fetchall()
                if existing_wafers:
                    trans.rollback()
                    return (
                        jsonify(
                            {
                                "success": False,
                                "message": (
                                    "Error: wafer_id already exists."
                                    f"({', '.join(w[0] for w in existing_wafers)})"
                                ),
                            }
                        ),
                        400,
                        response_headers,
                    )

                # 2. Vérifier l'existence de xfab_fr_lot_id et mask_set_id
                xfab_lot_exists = conn.execute(
                    text(
                        "SELECT lot_id FROM xfab_fr_lots "
                        "WHERE xfab_fr_lot_id = :xfab_fr_lot_id"
                    ),
                    {"xfab_fr_lot_id": xfab_fr_lot_id},
                ).fetchone()
                mask_set_exists = conn.execute(
                    text(
                        "SELECT mask_set_id "
                        "FROM mask_sets "
                        "WHERE mask_set_id = :mask_set_id"
                    ),
                    {"mask_set_id": mask_set_id},
                ).fetchone()
                if not xfab_lot_exists or not mask_set_exists:
                    trans.rollback()
                    return (
                        jsonify(
                            {
                                "success": False,
                                "message": (
                                    "Error: xfab_fr_lot_id or mask_set_id "
                                    "does not exist in their respective "
                                    "tables in ICARIUM"
                                ),
                            }
                        ),
                        400,
                        response_headers,
                    )

                # 8. Ajouter les wafers dans wafer_inventory
                results = []
                for wafer_id, slot_id in zip(wafer_id_list, slot_id_list):
                    conn.execute(
                        text(
                            "INSERT INTO wafer_inventory ("
                            "wafer_id, cassette_id, slot_id, arrived_at, sent_at, "
                            "location_id, metadata, updated_at, updated_by"
                            ") VALUES ("
                            ":wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at, "
                            ":location_id, :metadata, :updated_at, :updated_by)"
                        ),
                        {
                            "wafer_id": wafer_id,
                            "cassette_id": cassette_id,
                            "slot_id": slot_id,
                            "arrived_at": arrived_at or datetime.now(timezone.utc),
                            "sent_at": sent_at,
                            "location_id": location_id,
                            "metadata": json.dumps(
                                {
                                    "lgt": {
                                        "Modules": module_name,
                                        "xfab_lot_id": xfab_fr_lot_id,
                                        "mask_set_id": mask_set_id,
                                        "lgt_lot_id": lot_id,
                                    },
                                    "Modules": module_name,
                                }
                            ),
                            "updated_at": datetime.now(timezone.utc),
                            "updated_by": "system",
                        },
                    )
                    results.append(f"Success: wafer_id {wafer_id} added to inventory.")

                # Commit transaction
                trans.commit()

                return (
                    jsonify(
                        {
                            "success": True,
                            "message": "Inventory updated successfully",
                            "results": results,
                        }
                    ),
                    200,
                    response_headers,
                )

            except Exception as e:
                # Make sure to rollback transaction on error
                if trans.is_active:
                    trans.rollback()
                current_app.logger.error(
                    f"Error in add_inventory transaction: {str(e)}"
                )
                current_app.logger.error(traceback.format_exc())
                raise

    except psycopg2.errors.ForeignKeyViolation as fk_error:
        # Handle foreign key violation specifically
        current_app.logger.error(
            f"Foreign key violation in add_inventory: {str(fk_error)}"
        )

        # Extract constraint name to provide specific error message
        error_message = str(fk_error)

        if "fk_wafer_inventory_wafer_id_wafers" in error_message:
            user_message = (
                "Cannot add wafer to inventory: One or more wafer IDs do not exist in the main wafers table. "
                "Please ensure the wafer(s) are registered in the system before adding them to inventory."
            )
        else:
            # Generic foreign key error message for other constraints
            user_message = (
                "Cannot add wafer to inventory: Referenced data does not exist. "
                "Please verify all required information is valid and try again."
            )

        return (
            jsonify(
                {
                    "success": False,
                    "message": user_message,
                    "error_type": "foreign_key_violation",
                    "show_alert": True,
                }
            ),
            400,
            response_headers,
        )
    except Exception as e:
        # Ensure we always return a JSON response even on errors
        current_app.logger.error(f"Error in add_inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error adding to the wafer_inventory table: {str(e)}",
                }
            ),
            500,
            response_headers,
        )


@inventory_bp.route("/api/auth/check", methods=["GET"])
def check_auth():
    """Debug endpoint to check authentication status and permissions"""
    try:
        # Check if user is authenticated
        is_auth = is_authenticated()
        user_info = {}

        if is_auth:
            # Get current user info if authenticated
            user_id = session.get("user_id")
            if user_id:
                from core.auth.user_manager import UserManager

                user = UserManager.get_user_by_id(user_id)
                if user:
                    # Get permissions using the static method from UserManager
                    user_info = {
                        "id": user.get("id"),
                        "email": user.get("email"),
                        "role": user.get("role"),
                        "permissions": UserManager.get_user_permissions(
                            user.get("role", "")
                        ),
                    }

        # Return auth status and user info
        return jsonify(
            {
                "success": True,
                "authenticated": is_auth,
                "user": user_info,
                "session_data": {
                    k: v for k, v in session.items() if k not in ["_csrf_token"]
                },
            }
        )

    except Exception as e:
        # Handle unexpected errors
        current_app.logger.error(f"Error checking authentication: {str(e)}")
        current_app.logger.error(traceback.format_exc())

        # Return error response
        return (
            jsonify(
                {
                    "success": False,
                    "authenticated": False,
                    "message": f"Error checking authentication: {str(e)}",
                }
            ),
            500,
        )


# Helper functions for wafer ID correction
def validate_wafer_correction(data):
    """Validate wafer correction input data."""
    old_wafer_id = data.get("old_wafer_id")
    new_wafer_id = data.get("new_wafer_id")

    if not old_wafer_id or not new_wafer_id:
        return False, "Both old and new wafer IDs are required", None, None

    return True, "", old_wafer_id, new_wafer_id


def check_wafer_exists(conn, wafer_id):
    """Check if a wafer exists in the database."""
    check_query = text(
        """
        SELECT wafer_id, lot_id, size, metadata
        FROM wafers
        WHERE wafer_id = :wafer_id
    """
    )

    return conn.execute(check_query, {"wafer_id": wafer_id}).fetchone()


def prepare_correction_info(old_wafer_id, reason):
    """Prepare correction info for metadata."""
    return json.dumps(
        {
            "corrected_from": old_wafer_id,
            "correction_date": datetime.now(timezone.utc).isoformat(),
            "reason": reason,
        }
    )


def create_new_wafer(conn, old_wafer_id, new_wafer_id, correction_info):
    """Create new wafer with corrected ID."""
    insert_query = text(
        """
        WITH source_wafer AS (
            SELECT
                :new_wafer_id as wafer_id,
                lot_id,
                size,
                CASE
                    WHEN metadata IS NULL THEN
                        jsonb_build_object(
                            'correction_info',
                            :correction_info::jsonb
                        )
                    ELSE
                        jsonb_set(
                            metadata,
                            '{correction_info}',
                            :correction_info::jsonb
                        )
                END as metadata,
                :updated_at as updated_at,
                :updated_by as updated_by
            FROM wafers
            WHERE wafer_id = :old_wafer_id
        )
        INSERT INTO wafers (
            wafer_id, lot_id, size, metadata, updated_at, updated_by
        )
        SELECT * FROM source_wafer
    """
    )

    conn.execute(
        insert_query,
        {
            "new_wafer_id": new_wafer_id,
            "old_wafer_id": old_wafer_id,
            "correction_info": correction_info,
            "updated_at": datetime.now(timezone.utc),
            "updated_by": "system",
        },
    )


def update_inventory_references(conn, old_wafer_id, new_wafer_id):
    """Update inventory references to the new wafer ID."""
    update_query = text(
        """
        UPDATE wafer_inventory
        SET wafer_id = :new_wafer_id,
            updated_at = :updated_at,
            updated_by = :updated_by
        WHERE wafer_id = :old_wafer_id
        RETURNING wafer_id
    """
    )

    return conn.execute(
        update_query,
        {
            "new_wafer_id": new_wafer_id,
            "old_wafer_id": old_wafer_id,
            "updated_at": datetime.now(timezone.utc),
            "updated_by": "system",
        },
    )


def mark_old_wafer_corrected(conn, old_wafer_id, new_wafer_id):
    """Mark the old wafer as corrected in metadata."""
    update_query = text(
        """
        UPDATE wafers
        SET metadata = CASE
                WHEN metadata IS NULL THEN
                    jsonb_build_object(
                        'status', 'corrected',
                        'corrected_to', :new_wafer_id
                    )
                ELSE
                    metadata ||
                    jsonb_build_object(
                        'status', 'corrected',
                        'corrected_to', :new_wafer_id
                    )
            END,
            updated_at = :updated_at,
            updated_by = :updated_by
        WHERE wafer_id = :old_wafer_id
        RETURNING wafer_id
    """
    )

    return conn.execute(
        update_query,
        {
            "new_wafer_id": new_wafer_id,
            "old_wafer_id": old_wafer_id,
            "updated_at": datetime.now(timezone.utc),
            "updated_by": "system",
        },
    )


@inventory_bp.route("/api/inventory/correct-wafer", methods=["POST"])
@require_database()
@login_required
@permission_required("modify")
def correct_wafer_id():
    """Correct a wafer ID in the system."""
    try:
        data = request.get_json()
        correction_reason = data.get("reason", "")

        # Validate input data
        is_valid, error_message, old_wafer_id, new_wafer_id = validate_wafer_correction(
            data
        )
        if not is_valid:
            return jsonify({"success": False, "message": error_message}), 400

        # Get database connection
        engine = create_engine(get_db_url())

        with engine.connect() as conn:
            # Start transaction
            with conn.begin():
                try:
                    # Check if old wafer exists
                    old_wafer = check_wafer_exists(conn, old_wafer_id)
                    if not old_wafer:
                        return (
                            jsonify(
                                {
                                    "success": False,
                                    "message": (
                                        f"Original wafer ID {old_wafer_id} not found"
                                    ),
                                }
                            ),
                            404,
                        )

                    # Check if new ID already exists
                    exists = check_wafer_exists(conn, new_wafer_id)
                    if exists:
                        return (
                            jsonify(
                                {
                                    "success": False,
                                    "message": (
                                        f"Cannot correct to {new_wafer_id} - "
                                        "this ID already exists"
                                    ),
                                }
                            ),
                            400,
                        )

                    # Prepare correction info
                    correction_info = prepare_correction_info(
                        old_wafer_id, correction_reason
                    )

                    # Step 1: Create new wafer with corrected ID
                    create_new_wafer(conn, old_wafer_id, new_wafer_id, correction_info)

                    # Step 2: Update inventory references
                    update_inventory_references(conn, old_wafer_id, new_wafer_id)

                    # Step 3: Mark old wafer as corrected
                    mark_old_wafer_corrected(conn, old_wafer_id, new_wafer_id)

                    # All operations successful
                    return jsonify(
                        {
                            "success": True,
                            "message": (
                                f"Successfully corrected wafer ID from {old_wafer_id} "
                                f"to {new_wafer_id}"
                            ),
                            "details": {
                                "old_wafer_id": old_wafer_id,
                                "new_wafer_id": new_wafer_id,
                                "correction_time": datetime.now(
                                    timezone.utc
                                ).isoformat(),
                                "reason": correction_reason,
                            },
                        }
                    )

                except SQLAlchemyError as db_error:
                    # Log the specific database error
                    current_app.logger.error(f"Database error: {str(db_error)}")
                    # Transaction will automatically rollback
                    raise

    except Exception as e:
        # Log the full error with traceback
        current_app.logger.error(f"Error correcting wafer ID: {str(e)}")
        current_app.logger.error(traceback.format_exc())

        return (
            jsonify(
                {
                    "success": False,
                    "message": "Error correcting wafer ID",
                    "error_details": str(e),
                }
            ),
            500,
        )


@inventory_bp.route("/api/inventory/update_modules", methods=["POST"])
@require_database()
@login_required
@permission_required("modify")
def update_modules():
    try:
        data = request.get_json()
        if not data or not data.get("updates"):
            return (
                jsonify({"success": False, "message": "No update data provided"}),
                400,
            )

        # Should be list of {wafer_id: "", module_name: ""}
        updates = data["updates"]

        with get_db_cursor() as cursor:
            cursor.execute("BEGIN")
            try:
                for update in updates:
                    cursor.execute(
                        """
                        UPDATE wafer_inventory
                        SET metadata = jsonb_set(
                            jsonb_set(
                                COALESCE(metadata, '{}'::jsonb),
                                '{lgt}',
                                COALESCE(metadata->'lgt', '{}'::jsonb)
                            ),
                            '{lgt,Modules}',
                            %s::jsonb
                        ),
                        updated_at = NOW()
                        WHERE wafer_id = %s
                    """,
                        (json.dumps(update["module_name"]), update["wafer_id"]),
                    )

                    # Also update top-level Modules field if it exists
                    cursor.execute(
                        """
                        UPDATE wafer_inventory
                        SET metadata = jsonb_set(
                            metadata,
                            '{Modules}',
                            %s::jsonb,
                            true
                        )
                        WHERE wafer_id = %s AND metadata ? 'Modules'
                        """,
                        [json.dumps(update["module_name"]), update["wafer_id"]],
                    )

                cursor.execute("COMMIT")
                return jsonify(
                    {
                        "success": True,
                        "message": f"Successfully updated {len(updates)} records",
                    }
                )

            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e

    except Exception as e:
        current_app.logger.error(f"Error updating modules: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify({"success": False, "message": f"Error updating modules: {str(e)}"}),
            500,
        )


@inventory_bp.route("/api/inventory/delete", methods=["DELETE"])
@require_database()
@login_required
@permission_required("delete")
def delete_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Delete request data: {data}")

        wafer_ids = data.get("wafer_ids", [])
        if not wafer_ids:
            return jsonify({"success": False, "message": "No wafer IDs provided"}), 400

        with get_db_cursor() as cursor:
            cursor.execute("BEGIN")
            try:
                placeholders = ",".join(["%s"] * len(wafer_ids))
                cursor.execute(
                    f"""
                    DELETE FROM wafer_inventory
                    WHERE wafer_id IN ({placeholders})
                    RETURNING wafer_id
                """,
                    wafer_ids,
                )

                deleted_wafers = [row["wafer_id"] for row in cursor.fetchall()]

                if not deleted_wafers:
                    cursor.execute("ROLLBACK")
                    return (
                        jsonify(
                            {
                                "success": False,
                                "message": "No matching wafers found to delete",
                            }
                        ),
                        404,
                    )

                cursor.execute("COMMIT")
                return jsonify(
                    {
                        "success": True,
                        "message": (
                            f"Successfully deleted {len(deleted_wafers)} wafer(s)"
                        ),
                        "deleted_wafers": deleted_wafers,
                    }
                )

            except Exception as e:
                cursor.execute("ROLLBACK")
                raise e

    except Exception as e:
        current_app.logger.error(f"Error deleting inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error deleting inventory: {str(e)}"}
            ),
            500,
        )


@inventory_bp.route("/api/inventory/modify", methods=["PUT"])
@require_database()
@login_required
@permission_required("modify")
def modify_inventory():
    """Modify inventory entries."""
    try:
        data = request.get_json()
        current_app.logger.info(f"Modify request data: {data}")

        # Validate input data
        is_valid, error_message = validate_inventory_update(data)
        if not is_valid:
            return jsonify({"success": False, "message": error_message}), 400

        # Process the updates
        result = process_inventory_updates(data)

        # Add a cache buster to force fresh data when the UI reloads
        response_data = json.loads(result.data)
        response_data["cache_buster"] = datetime.now(timezone.utc).timestamp()

        # Return with cache headers to prevent browser caching
        response = jsonify(response_data)
        response.headers["Cache-Control"] = "no-cache, no-store, must-revalidate"
        response.headers["Pragma"] = "no-cache"
        response.headers["Expires"] = "0"

        return response

    except Exception as e:
        current_app.logger.error(f"Error modifying inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error modifying inventory: {str(e)}"}
            ),
            500,
        )


def process_inventory_updates(data):
    """
    Process updates for multiple wafers in inventory.

    Args:
        data: Request data dictionary

    Returns:
        flask.Response: JSON response with result
    """
    wafer_ids = data.get("wafer_ids", [])
    modified_wafers = []

    # Log the request data for debugging
    current_app.logger.info(f"Processing updates for wafers: {wafer_ids}")
    current_app.logger.info(f"Update data: {data}")

    with get_db_cursor() as cursor:
        cursor.execute("BEGIN")
        try:
            # Process updates for each wafer
            for wafer_id in wafer_ids:
                # Get old values before update for comparison
                old_values = get_wafer_inventory_values(cursor, wafer_id)
                if not old_values:
                    current_app.logger.warning(
                        f"Wafer {wafer_id} not found in inventory"
                    )
                    continue

                # Update metadata fields directly
                update_metadata_fields(cursor, wafer_id, data)

                # Update standard fields
                update_standard_fields(cursor, wafer_id, data)

                # Get updated values after changes
                cursor.execute(
                    """
                    SELECT * FROM wafer_inventory WHERE wafer_id = %s
                    """,
                    [wafer_id],
                )
                new_values = dict(cursor.fetchone())

                # Track the change
                track_wafer_change(cursor, wafer_id, "UPDATE", old_values, new_values)

                modified_wafers.append(wafer_id)
                current_app.logger.info(f"Successfully updated wafer {wafer_id}")

            cursor.execute("COMMIT")
            return jsonify(
                {
                    "success": True,
                    "message": "Successfully modified wafers in inventory",
                    "modified_wafers": modified_wafers,
                }
            )

        except Exception as e:
            cursor.execute("ROLLBACK")
            current_app.logger.error(f"Error in process_inventory_updates: {str(e)}")
            current_app.logger.error(traceback.format_exc())
            raise e


def update_standard_fields(cursor, wafer_id, data):
    """
    Update standard (non-metadata) fields for a wafer.

    Args:
        cursor: Database cursor
        wafer_id: ID of the wafer to update
        data: Request data dictionary

    Returns:
        bool: True if any fields were updated
    """
    # Check which fields to update
    fields_to_update = []
    values = []

    # Standard fields that can be updated
    if data.get("cassette_id") is not None:
        fields_to_update.append("cassette_id = %s")
        values.append(data["cassette_id"])

    if data.get("location_id") is not None:
        fields_to_update.append("location_id = %s")
        values.append(data["location_id"])

    # Handle date fields with special care and extra debug logging
    if data.get("arrived_at") is not None:
        # Log the arrived_at value for debugging
        current_app.logger.info(f"Arrived at value from request: {data['arrived_at']}")

        # If it's an empty string, keep the existing value
        if data["arrived_at"] == "":
            current_app.logger.info(
                f"Empty arrived_at value, keeping existing value for {wafer_id}"
            )
        else:
            # Use a simpler format without timezone to avoid browser parsing issues
            fields_to_update.append("arrived_at = %s")
            values.append(data["arrived_at"])
            current_app.logger.info(
                f"Setting arrived_at to {data['arrived_at']} for {wafer_id}"
            )
            # Log for verification
            current_app.logger.info(f"Type of arrived_at: {type(data['arrived_at'])}")

    if data.get("sent_at") is not None:
        # Log the sent_at value for debugging
        current_app.logger.info(f"Sent at value from request: {data['sent_at']}")

        # If it's an empty string, keep the existing value
        if data["sent_at"] == "":
            current_app.logger.info(
                f"Empty sent_at value, keeping existing value for {wafer_id}"
            )
        else:
            # Use a simpler format without timezone to avoid browser parsing issues
            fields_to_update.append("sent_at = %s")
            values.append(data["sent_at"])
            current_app.logger.info(
                f"Setting sent_at to {data['sent_at']} for {wafer_id}"
            )
            # Log for verification
            current_app.logger.info(f"Type of sent_at: {type(data['sent_at'])}")

    # Handle slot_id specially - check if this wafer has a specific slot update
    wafer_updates = data.get("wafer_updates", {})
    if wafer_id in wafer_updates and "slot_id" in wafer_updates[wafer_id]:
        # Individual slot_id for this wafer
        slot_id = wafer_updates[wafer_id]["slot_id"]
        current_app.logger.info(
            f"Using individual slot_id {slot_id} for wafer {wafer_id}"
        )
        fields_to_update.append("slot_id = %s")
        values.append(slot_id)
    elif data.get("slot_id") is not None:
        # Common slot_id for all wafers
        fields_to_update.append("slot_id = %s")
        values.append(data["slot_id"])

    # Always update timestamp and updated_by
    fields_to_update.extend(["updated_at = NOW()", "updated_by = %s"])
    values.append(data.get("updated_by", "system"))

    # Execute the update if we have fields to update
    if fields_to_update:
        query = f"""
            UPDATE wafer_inventory
            SET {", ".join(fields_to_update)}
            WHERE wafer_id = %s
        """
        values.append(wafer_id)
        cursor.execute(query, values)

        # Verify the update by fetching the updated record
        cursor.execute(
            """
            SELECT arrived_at, sent_at FROM wafer_inventory WHERE wafer_id = %s
            """,
            [wafer_id],
        )
        updated_record = cursor.fetchone()
        current_app.logger.info(
            f"After update, arrived_at = {updated_record['arrived_at']}, sent_at = {updated_record['sent_at']} for {wafer_id}"
        )

        return True

    return False


def ensure_json_response(app):
    """
    Middleware to ensure all JSON responses have the correct Content-Type header
    """

    @app.after_request
    def add_json_content_type(response):
        # Check if response is JSON
        if response.mimetype == "application/json":
            response.headers["Content-Type"] = "application/json"
        # For API routes, force JSON content type for error responses too
        elif request.path.startswith("/api/"):
            if response.status_code >= 400:
                # This is an error response for an API route
                # Check if the response is HTML
                content_type = response.headers.get("Content-Type", "")
                if "text/html" in content_type:
                    # This is an HTML error response for an API route
                    # Log that we're converting an HTML error to JSON
                    current_app.logger.warning(
                        (
                            f"Converting HTML error {response.status_code} to JSON "
                            f"for API route {request.path}"
                        )
                    )

                    # Convert it to JSON
                    error_message = "An error occurred"
                    if response.status_code == 401:
                        error_message = "Authentication required"
                    elif response.status_code == 403:
                        error_message = "Permission denied"
                    elif response.status_code == 404:
                        error_message = "Resource not found"
                    elif response.status_code == 500:
                        error_message = "Internal server error"

                    # Replace the response with a JSON response
                    response.data = json.dumps(
                        {
                            "success": False,
                            "message": error_message,
                            "status_code": response.status_code,
                        }
                    )
                    # Make sure to set the Content-Type header to application/json
                    response.headers["Content-Type"] = "application/json"
                    # Log the modified response
                    current_app.logger.info(f"Modified response: {response.data}")

        return response

    return app


def add_text_search_conditions(data, where_clause, params):
    text_search_fields = {
        "wafer_id": "wi.wafer_id ILIKE %s",
        "lot_id": "w.lot_id ILIKE %s",
        "xfab_id": "xfl.xfab_fr_lot_id ILIKE %s",
        "mask_set_id": "l.mask_set_id ILIKE %s",
        "module_name": "wi.metadata->'lgt'->>'Modules' ILIKE %s",
        "cassette_id": "wi.cassette_id ILIKE %s",
    }

    for field, condition in text_search_fields.items():
        if data.get(field):
            where_clause.append(condition)
            params.append(f"%{data[field].strip()}%")


def add_exact_match_conditions(data, where_clause, params):
    exact_match_fields = {
        "slot_id": "wi.slot_id = %s",
        "location_id": "wi.location_id = %s",
    }

    for field, condition in exact_match_fields.items():
        if data.get(field):
            where_clause.append(condition)
            params.append(data[field])


def add_date_range_conditions(data, where_clause, params):
    date_ranges = [
        ("arrived_at_from", "wi.arrived_at >= %s::timestamp", "00:00:00"),
        ("arrived_at_to", "wi.arrived_at <= %s::timestamp", "23:59:59"),
        ("sent_at_from", "wi.sent_at >= %s::timestamp", "00:00:00"),
        ("sent_at_to", "wi.sent_at <= %s::timestamp", "23:59:59"),
    ]

    for field, condition, time_suffix in date_ranges:
        if data.get(field):
            where_clause.append(condition)
            params.append(f"{data[field]} {time_suffix}")


def build_search_where_clause(data):
    where_clause = []
    params = []

    # Add different types of search conditions
    add_text_search_conditions(data, where_clause, params)
    add_exact_match_conditions(data, where_clause, params)
    add_date_range_conditions(data, where_clause, params)

    return where_clause, params


def get_inventory_sort_info(data):
    sort_field = data.get("sort_field")
    sort_direction = data.get("sort_direction", "asc").upper()

    valid_sort_fields = {
        "wafer_id": "wi.wafer_id",
        "lot_id": "w.lot_id",
        "xfab_id": "xfl.xfab_fr_lot_id",
        "module_name": "wi.metadata->'lgt'->>'Modules'",
        "cassette_id": "wi.cassette_id",
        "mask_set_id": "l.mask_set_id",
        "slot_id": "wi.slot_id",
        "location_id": "wi.location_id",
        "arrived_at": "wi.arrived_at",
        "sent_at": "wi.sent_at",
    }

    sort_clause = valid_sort_fields.get(sort_field, "wi.arrived_at")
    sort_direction = sort_direction if sort_direction in ["ASC", "DESC"] else "DESC"

    return sort_clause, sort_direction


def format_inventory_row(row):
    # Log the row data for debugging
    current_app.logger.info(
        (f"Current metadata for {row['wafer_id']}: " f"{row.get('metadata', {})}")
    )

    # Log the date fields for debugging
    if row["arrived_at"]:
        current_app.logger.info(
            f"Arrived at for {row['wafer_id']}: {row['arrived_at']} (type: {type(row['arrived_at'])})"
        )

    # Format the row data - store dates without timezone information to avoid browser issues
    formatted_row = {
        "row_number": row["row_num"],
        "wafer_id": row["wafer_id"],
        "lot_id": row["lot_id"],
        "xfab_id": row["xfab_id"],
        "module_name": row["module_name"],
        "cassette_id": row["cassette_id"],
        "mask_set_id": row["mask_set_id"],
        "slot_id": row["slot_id"],
        "location_id": row["location_id"],
        "location_label": row["location_label"],
        "arrived_at": (
            row["arrived_at"].replace(tzinfo=None).isoformat()
            if row["arrived_at"]
            else None
        ),
        "sent_at": (
            row["sent_at"].replace(tzinfo=None).isoformat() if row["sent_at"] else None
        ),
    }

    # Log the formatted row for debugging
    current_app.logger.info(
        f"Formatted row for {row['wafer_id']}: arrived_at={formatted_row['arrived_at']}, module_name={formatted_row['module_name']}"
    )

    return formatted_row


@inventory_bp.route("/api/inventory/search", methods=["POST"])
@require_database()
@login_required
def search_inventory():
    try:
        data = request.get_json()
        current_app.logger.info(f"Parsed JSON data: {data}")

        # Get pagination parameters
        page = int(data.get("page", 1))
        page_size = int(data.get("page_size", 10))
        offset = (page - 1) * page_size

        with get_db_cursor() as cursor:
            # Build base query
            base_query = """
                FROM wafer_inventory wi
                LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
                LEFT JOIN lots l ON w.lot_id = l.lot_id
                LEFT JOIN locations loc ON wi.location_id = loc.location_id
                LEFT JOIN xfab_fr_lots xfl ON w.lot_id = xfl.lot_id
                WHERE 1=1
            """

            # Build where clause and params
            where_clause, params = build_search_where_clause(data)

            # First get total count
            count_query = f"SELECT COUNT(*) as total {base_query}"
            if where_clause:
                count_query += " AND " + " AND ".join(where_clause)

            cursor.execute(count_query, params)
            total_records = cursor.fetchone()["total"]

            # Get sort information
            sort_clause, sort_direction = get_inventory_sort_info(data)

            # Get paginated data
            data_query = f"""
                WITH sorted_data AS (
                    SELECT
                        wi.wafer_id,
                        wi.cassette_id,
                        wi.slot_id,
                        wi.arrived_at as arrived_at,
                        wi.sent_at as sent_at,
                        wi.location_id,
                        COALESCE(w.lot_id, '') as lot_id,
                        COALESCE(xfl.xfab_fr_lot_id, '') as xfab_id,
                        COALESCE(wi.metadata->'lgt'->>'Modules', '') as module_name,
                        COALESCE(wi.metadata->'lgt'->>'wafer_mask_set_id', COALESCE(l.mask_set_id, '')) as mask_set_id,
                        COALESCE(loc.label, wi.location_id) as location_label,
                        wi.metadata as metadata,
                        ROW_NUMBER() OVER (
                            ORDER BY {sort_clause} {sort_direction}
                        ) as row_num
                    {base_query}
                    {' AND ' + ' AND '.join(where_clause) if where_clause else ''}
                )
                SELECT * FROM sorted_data
                LIMIT %s OFFSET %s
            """

            query_params = params + [page_size, offset]
            cursor.execute(data_query, query_params)
            results = cursor.fetchall()

            # Format results
            formatted_results = [format_inventory_row(row) for row in results]

            return jsonify(
                {
                    "success": True,
                    "data": formatted_results,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total_records,
                        "start": offset + 1,
                        "end": min(offset + page_size, total_records),
                        "total_pages": (total_records + page_size - 1) // page_size,
                    },
                    "mode": "online",
                }
            )

    except Exception as e:
        current_app.logger.error(f"Error in search_inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error searching inventory: {str(e)}",
                    "data": [],
                }
            ),
            500,
        )


@inventory_bp.route("/api/inventory/history/<wafer_id>", methods=["GET"])
@login_required
def get_wafer_history_endpoint(wafer_id):
    try:
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT metadata->'change_history' as history
                FROM wafer_inventory
                WHERE wafer_id = %s
            """,
                [wafer_id],
            )

            result = cursor.fetchone()
            # Correctly access the history from the result
            history = result["history"] if result and result["history"] else []

            return jsonify({"success": True, "history": history})

    except Exception as e:
        current_app.logger.error(f"Error fetching history: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify({"success": False, "message": f"Error fetching history: {str(e)}"}),
            500,
        )


def check_field_exist(connection, table_name: str, field_name: str, value: str) -> bool:
    """
    Check if a specific field value exists in a given table.

    Args:
        connection: Database connection object
        table_name: Name of the table to check
        field_name: Name of the field to check
        value: Value to check for existence

    Returns:
        True if the value exists, False otherwise
    """
    query = text(f"SELECT 1 FROM {table_name} WHERE {field_name} = :value LIMIT 1")
    result = connection.execute(query, {"value": value}).fetchone()
    return result is not None


def insert_wafer_inventory(db_url: str, wafer_data: WaferInventoryModel) -> bool:
    """
    Insert a single wafer into the wafer_inventory table with proper validation.

    Args:
        db_url: Database connection string
        wafer_data: WaferInventoryModel instance containing wafer data

    Returns:
        True if successful, False otherwise
    """
    engine = create_engine(db_url)

    with engine.connect() as connection:
        with connection.begin():
            try:
                # Check if wafer already exists in inventory
                if check_field_exist(
                    connection, "wafer_inventory", "wafer_id", wafer_data.wafer_id
                ):
                    print(f"Wafer {wafer_data.wafer_id} already exists in inventory")
                    return False

                # Build metadata
                inventory_metadata = {
                    "lgt": {
                        "Modules": wafer_data.metadata.get("lgt", {}).get(
                            "Modules", ""
                        ),
                        "xfab_lot_id": wafer_data.metadata.get("lgt", {}).get(
                            "xfab_lot_id", ""
                        ),
                        "lgt_lot_id": wafer_data.metadata.get("lgt", {}).get(
                            "lgt_lot_id", ""
                        ),
                    }
                }

                # Insert wafer into inventory
                insert_query = text(
                    """
                    INSERT INTO wafer_inventory (
                        wafer_id, cassette_id, slot_id, arrived_at, sent_at,
                        location_id, metadata, updated_at, updated_by
                    ) VALUES (
                        :wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at,
                        :location_id, :metadata, NOW(), :updated_by
                    )
                """
                )

                connection.execute(
                    insert_query,
                    {
                        "wafer_id": wafer_data.wafer_id,
                        "cassette_id": wafer_data.cassette_id,
                        "slot_id": wafer_data.slot_id,
                        "arrived_at": wafer_data.arrived_at
                        or datetime.now(timezone.utc),
                        "sent_at": wafer_data.sent_at,
                        "location_id": wafer_data.location_id,
                        "metadata": json.dumps(inventory_metadata),
                        "updated_by": wafer_data.updated_by or "system",
                    },
                )

                return True

            except Exception as e:
                print(f"Error inserting wafer inventory: {e}")
                raise


@inventory_bp.route("/api/inventory/sync-wafers", methods=["POST"])
@require_database()
@login_required
@permission_required("sync")
def sync_wafers_to_inventory():
    """
    Sync wafers from wafers table to wafer_inventory table
    with improved error handling and response formatting
    """
    # Always set content type to JSON
    response_headers = {"Content-Type": "application/json"}

    try:
        # Verify authentication again just to be safe
        if not is_authenticated():
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Authentication required",
                        "auth_error": True,
                    }
                ),
                401,
                response_headers,
            )

        # Get database URL
        db_url = get_db_url()

        # Initialize statistics
        stats = {
            "total_wafers": 0,  # Total wafers in wafers table
            "existing_wafers": 0,  # Wafers already in inventory
            "added": 0,  # New wafers added to inventory
            "errors": 0,  # Errors encountered
        }

        engine = create_engine(db_url)
        with engine.connect() as connection:
            # Start transaction
            trans = connection.begin()
            try:
                # Get total count of wafers in wafers table
                total_wafers_query = text("SELECT COUNT(*) FROM wafers")
                stats["total_wafers"] = connection.execute(total_wafers_query).scalar()

                # Get count of existing wafers in inventory
                existing_wafers_query = text("SELECT COUNT(*) FROM wafer_inventory")
                stats["existing_wafers"] = connection.execute(
                    existing_wafers_query
                ).scalar()

                # Find wafers that exist in wafers table but not in wafer_inventory
                missing_wafers_query = text(
                    """
                    SELECT COUNT(w.wafer_id)
                    FROM wafers w
                    LEFT JOIN wafer_inventory wi ON w.wafer_id = wi.wafer_id
                    WHERE wi.wafer_id IS NULL
                """
                )
                missing_count = connection.execute(missing_wafers_query).scalar()

                # If there are no missing wafers, return early
                if missing_count == 0:
                    trans.commit()
                    return (
                        jsonify(
                            {
                                "success": True,
                                "message": "All wafers are already in inventory.",
                                "total_wafers": stats["total_wafers"],
                                "existing_wafers": stats["existing_wafers"],
                                "added": 0,
                                "skipped": stats["existing_wafers"],
                                "errors": 0,
                            }
                        ),
                        200,
                        response_headers,
                    )

                # Execute the populate function with the new missing wafers
                try:
                    added_count = populate_wafer_inventory_from_wafers(db_url)
                    stats["added"] = added_count or 0
                    trans.commit()
                except Exception as populate_error:
                    trans.rollback()
                    current_app.logger.error(
                        f"Error in populate_wafer_inventory_from_wafers: "
                        f"{str(populate_error)}"
                    )
                    current_app.logger.error(traceback.format_exc())
                    stats["errors"] += 1
                    return (
                        jsonify(
                            {
                                "success": False,
                                "message": (
                                    f"Error syncing wafers to inventory: "
                                    f"{str(populate_error)}"
                                ),
                                "total_wafers": stats["total_wafers"],
                                "existing_wafers": stats["existing_wafers"],
                                "added": 0,
                                "errors": 1,
                                "error_details": str(populate_error),
                            }
                        ),
                        500,
                        response_headers,
                    )

                return (
                    jsonify(
                        {
                            "success": True,
                            "message": (
                                f"Sync complete. Added {stats['added']} new wafers."
                            ),
                            "total_wafers": stats["total_wafers"],
                            "existing_wafers": stats["existing_wafers"],
                            "added": stats["added"],
                            "skipped": stats["existing_wafers"],
                            "errors": stats["errors"],
                        }
                    ),
                    200,
                    response_headers,
                )

            except Exception as db_error:
                # Make sure to rollback transaction on error
                if trans.is_active:
                    trans.rollback()

                current_app.logger.error(
                    f"Database error in sync_wafers_to_inventory: {str(db_error)}"
                )
                current_app.logger.error(traceback.format_exc())
                stats["errors"] += 1

                return (
                    jsonify(
                        {
                            "success": False,
                            "message": (
                                f"Database error syncing wafers: {str(db_error)}"
                            ),
                            "total_wafers": stats["total_wafers"],
                            "existing_wafers": stats["existing_wafers"],
                            "added": 0,
                            "errors": 1,
                            "error_details": str(db_error),
                        }
                    ),
                    500,
                    response_headers,
                )

    except Exception as e:
        current_app.logger.error(f"Error syncing wafers to inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())

        # Always return JSON
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error syncing wafers to inventory: {str(e)}",
                    "error_details": str(e),
                }
            ),
            500,
            response_headers,
        )


def populate_wafer_inventory_from_wafers(db_url: str) -> int:
    """
    Improved version that populates wafer_inventory with data from wafers.
    Assigns a slot_id based on the order of wafers in a given lot.

    Args:
        db_url: Database connection string

    Returns:
        Number of wafers added to inventory
    """
    engine = create_engine(db_url)
    wafers_added = 0

    with engine.connect() as connection:
        # Start an explicit transaction
        with connection.begin():
            try:
                # Step 1: Get existing wafers in inventory
                current_app.logger.info("Getting existing wafers in inventory...")
                existing_wafer_query = text(
                    """
                    SELECT wafer_id FROM wafer_inventory
                """
                )
                existing_wafers = {
                    row[0]
                    for row in connection.execute(existing_wafer_query).fetchall()
                }
                current_app.logger.info(
                    f"Found {len(existing_wafers)} existing wafers in inventory"
                )

                # Step 2: Get wafers with their lots that need to be added
                current_app.logger.info("Fetching wafers to add...")
                fetch_query = text(
                    """
                    SELECT w.wafer_id, w.lot_id
                    FROM wafers w
                    JOIN lots l ON w.lot_id = l.lot_id
                    WHERE w.wafer_id NOT IN (
                        SELECT wafer_id FROM wafer_inventory
                    )
                    ORDER BY w.lot_id, w.wafer_id
                """
                )
                records = connection.execute(fetch_query).fetchall()
                current_app.logger.info(
                    f"Found {len(records)} wafers to add to inventory"
                )

                if not records:
                    current_app.logger.info("No new wafers to add")
                    return 0

                # Step 3: Prepare data for wafer_inventory
                inventory_data = []
                lot_id_map = {}

                for record in records:
                    wafer_id, lot_id = record

                    # Skip if wafer already exists
                    if wafer_id in existing_wafers:
                        continue

                    # Determine slot_id for this wafer_id
                    if lot_id not in lot_id_map:
                        # For each new lot, get the max existing slot_id
                        slot_query = text(
                            """
                            SELECT MAX(slot_id)
                            FROM wafer_inventory
                            WHERE wafer_id IN (
                                SELECT wafer_id FROM wafers WHERE lot_id = :lot_id
                            )
                        """
                        )
                        max_slot = connection.execute(
                            slot_query, {"lot_id": lot_id}
                        ).scalar()
                        lot_id_map[lot_id] = max_slot or 0

                    # Increment slot for this lot
                    lot_id_map[lot_id] += 1
                    slot_id = lot_id_map[lot_id]

                    # Build metadata for wafer_inventory
                    # Try to get xfab_lot_id if available
                    xfab_query = text(
                        """
                        SELECT xfab_fr_lot_id
                        FROM xfab_fr_lots
                        WHERE lot_id = :lot_id
                    """
                    )
                    xfab_result = connection.execute(
                        xfab_query, {"lot_id": lot_id}
                    ).fetchone()
                    xfab_id = xfab_result[0] if xfab_result else ""

                    # Create metadata structure
                    wafer_metadata = {
                        "lgt": {
                            "Modules": "",
                            "xfab_lot_id": xfab_id,
                            "lgt_lot_id": lot_id,
                        }
                    }

                    # Add to inventory data
                    inventory_data.append(
                        {
                            "wafer_id": wafer_id,
                            "cassette_id": (
                                f"Auto-{lot_id[:8]}" if lot_id else "Auto-generated"
                            ),
                            "slot_id": slot_id,
                            "arrived_at": datetime.now(timezone.utc),
                            "sent_at": None,
                            "location_id": "xfab_fr",  # Default location
                            "metadata": json.dumps(wafer_metadata),
                            "updated_at": datetime.now(timezone.utc),
                            "updated_by": "system",
                        }
                    )

                # Step 4: Insert data into wafer_inventory
                if inventory_data:
                    insert_query = text(
                        """
                        INSERT INTO wafer_inventory (
                            wafer_id, cassette_id, slot_id, arrived_at, sent_at,
                            location_id, metadata, updated_at, updated_by
                        ) VALUES (
                            :wafer_id, :cassette_id, :slot_id, :arrived_at, :sent_at,
                            :location_id, :metadata, :updated_at, :updated_by
                        )
                    """
                    )

                    # Process in batches to avoid excessive memory usage
                    batch_size = 100
                    for i in range(0, len(inventory_data), batch_size):
                        batch = inventory_data[i : i + batch_size]
                        connection.execute(insert_query, batch)
                        current_app.logger.info(
                            f"Inserted batch of {len(batch)} records"
                        )
                        wafers_added += len(batch)

                current_app.logger.info(
                    f"wafer_inventory populated successfully "
                    f"({wafers_added} rows added)"
                )
                return wafers_added

            except Exception as e:
                current_app.logger.error(f"Error populating wafer_inventory: {e}")
                current_app.logger.error(traceback.format_exc())
                raise


# Debug endpoint to check auth and database connectivity
@inventory_bp.route("/api/inventory/sync-wafers-debug", methods=["GET"])
@require_database()
@login_required
def debug_sync_wafers():
    """Debug endpoint to check auth and database connectivity"""
    try:
        # Check database connection
        engine = create_engine(get_db_url())
        with engine.connect() as conn:
            # Simple query to verify connection
            result = conn.execute(text("SELECT 1 as test")).fetchone()

        # Check that we can get a proper JSON response
        response_data = {
            "success": True,
            "message": "Debug endpoint working correctly",
            "connection_test": bool(result and result[0] == 1),
            "user_info": {
                "authenticated": True,
                "has_sync_permission": True,  # This should be checked properly
            },
        }

        return jsonify(response_data)

    except Exception as e:
        # Log the full error with traceback
        current_app.logger.error(f"Debug endpoint error: {str(e)}")
        current_app.logger.error(traceback.format_exc())

        # Return a properly formatted error response
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"Error: {str(e)}",
                    "error_type": "debug_error",
                }
            ),
            500,
        )


@inventory_bp.route("/api/inventory/export", methods=["POST"])
@require_database()
@login_required
def export_inventory():
    try:
        data = request.get_json()

        # Get the data - reuse the search logic from search_inventory
        with get_db_cursor() as cursor:
            # Build base query
            base_query = """
                FROM wafer_inventory wi
                LEFT JOIN wafers w ON wi.wafer_id = w.wafer_id
                LEFT JOIN lots l ON w.lot_id = l.lot_id
                LEFT JOIN locations loc ON wi.location_id = loc.location_id
                LEFT JOIN xfab_fr_lots xfl ON w.lot_id = xfl.lot_id
                WHERE 1=1
            """

            # Build where clause and params
            where_clause, params = build_search_where_clause(data)

            # Get sort information
            sort_clause, sort_direction = get_inventory_sort_info(data)

            # Get all data without pagination for export
            data_query = f"""
                SELECT
                    wi.wafer_id,
                    wi.cassette_id,
                    wi.slot_id,
                    wi.arrived_at,
                    wi.sent_at,
                    wi.location_id,
                    COALESCE(w.lot_id, '') as lot_id,
                    COALESCE(xfl.xfab_fr_lot_id, '') as xfab_id,
                    COALESCE(wi.metadata->>'Modules', '') as module_name,
                    COALESCE(l.mask_set_id, '') as mask_set_id,
                    COALESCE(loc.label, wi.location_id) as location_label
                {base_query}
                {' AND ' + ' AND '.join(where_clause) if where_clause else ''}
                ORDER BY {sort_clause} {sort_direction}
            """

            cursor.execute(data_query, params)
            results = cursor.fetchall()

            # Format results
            formatted_results = []
            for row in results:
                formatted_result = {
                    "wafer_id": row["wafer_id"],
                    "lot_id": row["lot_id"],
                    "xfab_id": row["xfab_id"],
                    "module_name": row["module_name"],
                    "cassette_id": row["cassette_id"],
                    "mask_set_id": row["mask_set_id"],
                    "slot_id": row["slot_id"],
                    "location_id": row["location_id"],
                    "location_label": row["location_label"],
                    "arrived_at": (
                        row["arrived_at"].isoformat() if row["arrived_at"] else None
                    ),
                    "sent_at": row["sent_at"].isoformat() if row["sent_at"] else None,
                }
                formatted_results.append(formatted_result)

            return jsonify({"success": True, "data": formatted_results})

    except Exception as e:
        current_app.logger.error(f"Error exporting inventory: {str(e)}")
        current_app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"Error exporting inventory: {str(e)}"}
            ),
            500,
        )
