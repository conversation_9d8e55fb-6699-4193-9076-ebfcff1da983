"""Talaria Dashboard - Wafer inventory management for Ligentec SA.

Developed by <PERSON><PERSON>, September 2024 V1.0.0
"""

# Standard library imports
import base64

# import json
import io
import json
import logging
import os
import platform
import re
import socket
import subprocess
import time
import traceback
from datetime import datetime, timedelta
from io import BytesIO, String<PERSON>
from pathlib import Path
from typing import Any, Dict, List, Tuple

# Third party imports
import asana
import matplotlib.pyplot as plt
import pandas as pd
import psutil
import qrcode
from dotenv import load_dotenv
from flask import (
    Flask,
    current_app,
    flash,
    g,
    jsonify,
    make_response,
    redirect,
    render_template,
    request,
    send_file,
    session,
    url_for,
)
from flask_cors import CORS
from flask_mail import Mail, Message
from flask_socketio import SocketIO
from flask_wtf.csrf import CSRFProtect, generate_csrf
from openpyxl import Workbook
from PIL import Image, ImageDraw, ImageFont
from reportlab.lib.pagesizes import A4
from reportlab.lib.utils import ImageReader
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.pdfgen import canvas
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Local application imports
from api.api import api_blueprint
from api.preferences import preferences_bp
from api.profile import profile_bp
from api.support import support_bp
from core.auth.auth import (
    auth_bp,
    check_permission,
    login_required,
)
from core.auth.forms import AdvancedSearchForm, PrintLabelForm, SelectLotForm
from core.auth.user_routes import user_bp
from routes.chat_routes import chat_bp
from routes.chat_training_routes import chat_training_bp

try:
    from core.services import DashboardService

    print("Successfully imported DashboardService from core.services")
except ImportError as e:
    print(f"Error importing DashboardService from core.services: {e}")
    try:
        from core.services.cached_dashboard_service import DashboardService

        print(
            "Successfully imported DashboardService from "
            "core.services.cached_dashboard_service"
        )
    except ImportError as e:
        print(
            (
                f"Error importing DashboardService from "
                f"core.services.cached_dashboard_service: {e}"
            )
        )
        raise
from core.services.send_email import send_shipping_notification, send_test_email
from database.db_config import (
    DATABASE_URL,
    get_db_connection,
    get_db_cursor,
    test_connection,
)
from database.db_operations import (
    extract_task_gid,
    get_all_lots,
    get_asana_task_info,
    get_inventory_stats,
    get_shipment_stats_from_asana,
    get_wafers_for_lot,
    search_wafers,
    update_wafer_locations,
    validate_wafers,
)
from routes.dashboard_routes import dashboard_bp
from routes.inventory_routes import ensure_json_response, inventory_bp
from routes.location_routes import location_bp
from routes.shipment_routes import shipment_bp
from routes.ups_routes import ups_bp
from routes.ups_views import ups_views_bp
from utils.printer_monitor import setup_printer_monitoring
from utils.settings_manager import (
    apply_settings,
    load_settings,
    save_settings,
    validate_settings,
)
from utils.system_monitor import SystemMonitor

# Set up logging
log_level = logging.DEBUG if os.getenv("FLASK_ENV") == "development" else logging.INFO
logging.basicConfig(
    level=log_level, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Database connection test
if test_connection():
    logger.info("Database connection successful!")
else:
    logger.warning("Database connection failed.")

# System-agnostic path configurations
DOWNLOAD_FOLDER = os.getenv("DOWNLOAD_FOLDER", str(Path.home() / "Downloads"))
print(f"Using download folder: {DOWNLOAD_FOLDER}")

# Create the directory if it doesn't exist
try:
    os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)
except Exception as e:
    print(f"Warning: Could not create download folder: {str(e)}")
    logger.warning(f"Could not create download folder: {str(e)}")


def get_system_font_path():
    """Get font path based on operating system."""
    system = platform.system()
    if system == "Windows":
        font_paths = ["C:\\Windows\\Fonts\\Arial.ttf", "C:\\Windows\\Fonts\\arial.ttf"]
    elif system == "Darwin":  # macOS
        font_paths = [
            "/System/Library/Fonts/Supplemental/Arial.ttf",
            "/Library/Fonts/Arial.ttf",
            str(Path.home() / "Library/Fonts/Arial.ttf"),
        ]
    else:  # Linux and other Unix
        font_paths = [
            # Custom Arial path (created in Dockerfile)
            "/usr/share/fonts/truetype/custom/Arial.ttf",
            # Standard Arial paths
            "/usr/share/fonts/truetype/arial/Arial.ttf",
            "/usr/share/fonts/TTF/Arial.ttf",
            "/usr/share/fonts/truetype/msttcorefonts/Arial.ttf",
            # DejaVu Sans paths for Alpine and other Linux
            "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/TTF/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            # Liberation Sans paths
            "/usr/share/fonts/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            # Noto Sans paths
            "/usr/share/fonts/noto/NotoSans-Regular.ttf",
            "/usr/share/fonts/truetype/noto/NotoSans-Regular.ttf",
        ]

    # Try each path
    for path in font_paths:
        if os.path.exists(path):
            print(f"Found font: {path}")
            return path

    # Last resort - find any TTF font
    try:
        import glob

        # Search patterns from most specific to most general
        patterns = [
            "/usr/share/fonts/**/DejaVu*.ttf",
            "/usr/share/fonts/**/Liberation*.ttf",
            "/usr/share/fonts/**/Noto*.ttf",
            "/usr/share/fonts/**/*.ttf",
            "/usr/share/fonts/*.ttf",
        ]

        for pattern in patterns:
            fonts = glob.glob(pattern, recursive=True)
            if fonts:
                print(f"Using fallback font: {fonts[0]}")
                return fonts[0]
    except Exception as e:
        logger.warning(f"Error searching for fallback fonts: {str(e)}")
        pass

    # If we get here, we couldn't find any font
    logger.error("No suitable font found in system. PDF generation may fail.")
    raise FileNotFoundError("No suitable font found in system.")


# Set up font path
try:
    FONT_PATH = get_system_font_path()
    font_name = os.path.basename(FONT_PATH).split(".")[0]

    # Wrap font registration in try/except
    try:
        pdfmetrics.registerFont(TTFont(font_name, FONT_PATH))
        # Ensure "Arial" is available as an alias if we found a different font
        if font_name != "Arial":
            pdfmetrics.registerFontFamily("Arial", normal=font_name)
        print(f"Successfully registered font: {font_name} from {FONT_PATH}")
    except Exception as e:
        print(f"Warning: Error registering font {font_name}: {str(e)}")
        # Use Helvetica (a ReportLab built-in font) as fallback
        font_name = "Helvetica"
        print(f"Using fallback font: {font_name}")

except FileNotFoundError as e:
    print(f"Warning: {str(e)} Using fallback font.")
    # Try to use Helvetica as a fallback
    font_name = "Helvetica"
    print(f"Using fallback font: {font_name}")
except Exception as e:
    print(f"Could not register any fallback font: {str(e)}. PDF generation may fail.")
    logger.error(f"Font registration error: {str(e)}")
    # Last resort - try to use any available standard font
    font_name = "Helvetica"
    print(f"Using last-resort fallback font: {font_name}")


def get_safe_font_name(font_name=None):
    """Get a font name that is guaranteed to be available in the system."""
    # List of built-in ReportLab fonts that are always available
    builtin_fonts = ["Helvetica", "Courier", "Times-Roman"]

    try:
        # If a specific font is requested, try to use it
        if font_name:
            # Special case for DejaVuSans
            if font_name.lower() == "dejavusans":
                # Check if DejaVuSans is already registered
                if "DejaVuSans" in pdfmetrics.getRegisteredFontNames():
                    app.logger.info("Using previously registered DejaVuSans font")
                    return "DejaVuSans"

                # Try to register DejaVuSans if not already registered
                dejavu_paths = [
                    os.environ.get(
                        "DEJAVU_FONT_PATH"
                    ),  # Check environment variable first
                    "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf",  # Alpine path
                    "/usr/share/fonts/dejavu/DejaVuSans.ttf",  # Symlinked path
                    "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Debian/Ubuntu path
                    "/usr/share/fonts/TTF/DejaVuSans.ttf",  # Possible path on some systems
                ]

                for path in dejavu_paths:
                    if path and os.path.exists(path):
                        try:
                            pdfmetrics.registerFont(TTFont("DejaVuSans", path))
                            app.logger.info(f"Registered DejaVuSans font from {path}")
                            return "DejaVuSans"
                        except Exception as e:
                            app.logger.error(
                                f"Failed to register DejaVuSans from {path}: {e}"
                            )

                # If we couldn't register DejaVuSans, fall back to Helvetica
                app.logger.warning(
                    "Could not register DejaVuSans, falling back to Helvetica"
                )
                return "Helvetica"

            # For other fonts, check if they're already registered
            if font_name in pdfmetrics.getRegisteredFontNames():
                return font_name

        # Check for environment-forced font
        force_font = os.environ.get("FORCE_FONT")
        if force_font:
            app.logger.info(f"Using environment-forced font: {force_font}")

            # If the forced font is a built-in font, use it directly
            if force_font in builtin_fonts:
                return force_font

            # If the forced font is already registered, use it
            if force_font in pdfmetrics.getRegisteredFontNames():
                return force_font

            # Otherwise, fall back to Helvetica
            app.logger.warning(
                f"Forced font {force_font} not available, using Helvetica"
            )
            return "Helvetica"

        # Check for Arial (commonly registered earlier in the app)
        if "Arial" in pdfmetrics.getRegisteredFontNames():
            app.logger.info("Using previously registered Arial font")
            return "Arial"

        # Final fallback to Helvetica (always available in ReportLab)
        app.logger.info("Using Helvetica as fallback font")
        return "Helvetica"

    except Exception as e:
        app.logger.error(f"Error in get_safe_font_name: {str(e)}")
        return "Helvetica"  # Ultimate fallback


# Initialize Flask app
app = Flask(__name__, template_folder="templates")
csrf = CSRFProtect()
load_dotenv()
CORS(app)
# Apply the JSON response middleware using the imported function
app = ensure_json_response(
    app
)  # This function is imported from routes.inventory_routes

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Register fonts for ReportLab
try:
    # First try the environment path
    dejavu_path = os.environ.get(
        "DEJAVU_FONT_PATH", "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf"
    )

    if os.path.exists(dejavu_path):
        pdfmetrics.registerFont(TTFont("DejaVuSans", dejavu_path))
        app.logger.info(f"Successfully registered DejaVuSans from {dejavu_path}")
    else:
        # Try to find it by scanning paths
        for path in [
            "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/TTF/DejaVuSans.ttf",
        ]:
            if os.path.exists(path):
                pdfmetrics.registerFont(TTFont("DejaVuSans", path))
                app.logger.info(f"Successfully registered DejaVuSans from {path}")
                break
        else:
            # No DejaVu found, register Helvetica alias
            app.logger.warning("DejaVu font not found, using Helvetica")

    # Also register Arial if available
    arial_path = os.environ.get(
        "FONT_PATH", "/usr/share/fonts/truetype/custom/Arial.ttf"
    )
    if os.path.exists(arial_path):
        try:
            pdfmetrics.registerFont(TTFont("Arial", arial_path))
            app.logger.info(f"Successfully registered Arial from {arial_path}")
        except Exception as arial_error:
            app.logger.error(f"Error registering Arial font: {arial_error}")

except Exception as e:
    app.logger.error(f"Error registering DejaVuSans font: {e}")
    app.logger.warning("Falling back to Helvetica")


# Add this function BEFORE the app.app_context() block
def get_secret_or_env(secret_name, env_name=None, fallback=None):
    """Read from Docker secret file or environment variable"""
    if env_name is None:
        env_name = secret_name.upper()

    # Try Docker secret first
    secret_path = f"/run/secrets/{secret_name}"
    if os.path.exists(secret_path):
        try:
            with open(secret_path, "r") as f:
                return f.read().strip()
        except Exception:
            pass

    # Fall back to environment variable
    return os.getenv(env_name, fallback)


with app.app_context():
    # Core configuration
    app.config["SECRET_KEY"] = get_secret_or_env(
        "secret_key", "SECRET_KEY", "dev-key-for-testing-only-not-for-production"
    )
    app.config["WTF_CSRF_ENABLED"] = True
    app.config["WTF_CSRF_TIME_LIMIT"] = 3600
    app.config["JSON_AS_ASCII"] = False
    app.config["JSON_SORT_KEYS"] = False

    # Log secret key status (without revealing the actual key)
    secret_source = (
        "Docker secrets"
        if os.path.exists("/run/secrets/secret_key")
        else "environment variables"
    )
    if get_secret_or_env("secret_key", "SECRET_KEY"):
        app.logger.info(f"Using SECRET_KEY from {secret_source}")
    else:
        app.logger.warning(
            "No SECRET_KEY found in Docker secrets or environment variables, "
            "using default development key"
        )
    app.config["DB_CONNECTION"] = os.getenv("DB_CONNECTION", DATABASE_URL)
    app.config["DEFAULT_PRINTER_IP"] = os.getenv("DEFAULT_PRINTER_IP", "*************")
    app.config["UPLOAD_FOLDER"] = os.getenv(
        "UPLOAD_FOLDER", os.path.join(os.path.dirname(__file__), "uploads")
    )

    # Add DOWNLOAD_FOLDER to app config
    app.config["DOWNLOAD_FOLDER"] = DOWNLOAD_FOLDER

    # Initialize CSRF
    csrf.init_app(app)

    # Initialize test routes
    # init_test_routes(app)

    # Use database URL from environment or configuration
    app.config["DATABASE_URL"] = os.getenv("DATABASE_URL", DATABASE_URL)

    # Configure Flask-Mail from environment variables - using database/.env
    # Load database/.env file for email configuration
    db_env_path = os.path.join(os.path.dirname(__file__), "database", ".env")
    if os.path.exists(db_env_path):
        with open(db_env_path) as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith("#") and "=" in line:
                    key, value = line.split("=", 1)
                    os.environ[key] = value

    # Configure Flask-Mail to use SMTP relay without authentication
    app.config.update(
        MAIL_SERVER=os.getenv("MAIL_SERVER", "smtp-relay.gmail.com"),
        MAIL_PORT=int(os.getenv("MAIL_PORT", 587)),
        MAIL_USE_TLS=os.getenv("MAIL_USE_TLS", "True").lower() == "true",
        MAIL_USE_SSL=os.getenv("MAIL_USE_SSL", "False").lower() == "true",
        # No username/password for SMTP relay
        MAIL_USERNAME=None,
        MAIL_PASSWORD=None,
        MAIL_DEFAULT_SENDER=os.getenv("MAIL_DEFAULT_SENDER", "<EMAIL>"),
    )

    # Register blueprints
    app.register_blueprint(api_blueprint, url_prefix="/api/v1")
    app.register_blueprint(auth_bp, url_prefix="/auth")
    app.register_blueprint(inventory_bp)
    app.register_blueprint(location_bp)
    app.register_blueprint(shipment_bp)
    # app.register_blueprint(test_dashboard)
    app.register_blueprint(dashboard_bp)
    app.register_blueprint(user_bp, url_prefix="/users")
    app.register_blueprint(ups_bp)
    app.register_blueprint(ups_views_bp)
    app.register_blueprint(profile_bp)
    app.register_blueprint(preferences_bp)
    app.register_blueprint(support_bp)
    app.register_blueprint(chat_bp)
    app.register_blueprint(chat_training_bp, url_prefix="/chat")

    # Apply middleware to ensure JSON responses for API routes
    app = ensure_json_response(app)

    # Initialize the dashboard service with caching enabled and 5-minute TTL
    asana_token = os.getenv("ASANA_TOKEN")

    # Debug log to check if ASANA_TOKEN is loaded
    if asana_token:
        app.logger.info(f"Found ASANA_TOKEN: {asana_token[:5]}...")
    else:
        app.logger.warning("ASANA_TOKEN not found in environment variables")
        # Fallback to a hardcoded token if needed
        asana_token = "***************************************************"
        app.logger.info("Using fallback ASANA_TOKEN")

    # Create dashboard service with 15-minute cache TTL for better performance
    # Increased from 5 minutes (300s) to 15 minutes (900s) to reduce API calls and improve load times
    dashboard_service = DashboardService(asana_token, cache_ttl=900)

    if dashboard_service.initialize_client():
        app.logger.info("Dashboard service initialized successfully with caching")
        # Start background data refresh
        dashboard_service.start_background_refresh()
    else:
        app.logger.warning("Failed to initialize dashboard service")

    app.config["DASHBOARD_SERVICE"] = dashboard_service

    # Register a function to stop the background refresh thread when the app shuts down
    @app.teardown_appcontext
    def shutdown_dashboard_service(_=None):
        dashboard_service = app.config.get("DASHBOARD_SERVICE")
        if dashboard_service:
            dashboard_service.stop_background_refresh()

    # Create database engine
    engine = create_engine(app.config["DATABASE_URL"])
    Session = sessionmaker(bind=engine)

    # Start printer monitoring
    setup_printer_monitoring(app)

# Initialize components that need configured app
socketio = SocketIO(app)
mail = Mail(app)
system_monitor = SystemMonitor()

# Initialize prediction client
# client = PredictionClient()
# os.environ['ABACUSAI_API_KEY'] = os.getenv('ABACUSAI_API_KEY')

# Define constants
GENERAL_LOGO_PATH = "static/img/logo.gif"
ERFURT_LOGO_PATH = "static/img/logo.gif"

# Logger is already set up at the top of the file


@app.before_request
def check_remember_me():
    """Check for remember token cookie and log in user if valid"""
    # Skip for login/logout routes and if user is already logged in
    if request.path.startswith("/auth/") or session.get("user_id"):
        return

    # Check for remember token cookie
    remember_token = request.cookies.get("remember_token")
    if remember_token:
        from core.auth.user_manager import UserManager

        # Get user by remember token
        user = UserManager.get_user_by_remember_token(remember_token)
        if user:
            # Log in the user
            session["user_id"] = user["id"]
            session["user_email"] = user["email"]
            session["user_role"] = user["role"]
            session["user_name"] = user["name"]

            # Update last login time
            UserManager.update_last_login(user["id"])


@app.before_request
def handle_ajax_auth():
    # If this is an AJAX request to an API endpoint and user is not authenticated
    if (
        request.path.startswith("/api/")
        and request.headers.get("X-Requested-With") == "XMLHttpRequest"
    ):
        # Add X-Requested-With header to your JavaScript fetch calls
        from core.auth.auth import is_authenticated

        if not is_authenticated():
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Authentication required",
                        "status_code": 401,
                    }
                ),
                401,
            )


# Error handlers
@app.errorhandler(400)
def handle_bad_request(e):
    if "csrf" in str(e).lower():
        return (
            jsonify({"success": False, "message": "CSRF token is missing or invalid"}),
            400,
        )
    return jsonify({"success": False, "message": str(e)}), 400


@app.errorhandler(404)
def handle_not_found(_):
    return jsonify({"success": False, "message": "Resource not found"}), 404


@app.errorhandler(500)
def handle_server_error(e):
    logger.error(f"Server error: {str(e)}")
    return jsonify({"success": False, "message": "Internal server error"}), 500


# CSRF token injection
@app.context_processor
def inject_csrf_token():
    """Inject CSRF token into all templates"""
    return dict(csrf_token=generate_csrf())


@app.before_request
def log_request_info():
    """Log request information but only in debug mode to avoid cluttering logs"""
    if app.debug:
        logger.debug(f"Request: {request.method} {request.path}")
        logger.debug(f"Headers: {request.headers}")
        if request.method in ["POST", "PUT", "PATCH"]:
            logger.debug(f"Body: {request.get_data()}")

    # Add request start time for performance monitoring
    g.start_time = time.time()


def sanitize_filename(filename):
    """Clean a string to make it safe for use in filenames.
    Remove or replace invalid characters.
    """
    # Replace spaces with underscores
    filename = filename.replace(" ", "_")
    # Remove any characters that aren't alphanumeric, dash, or underscore
    filename = re.sub(r"[^a-zA-Z0-9-_]", "", filename)
    # Limit length to avoid excessively long filenames
    return filename[:50]


@app.route("/api/inventory")
def get_inventory():
    """Get inventory data from the database for API endpoint"""
    try:
        with get_db_cursor() as cursor:
            # Use SQL to get inventory data
            cursor.execute(
                """
                SELECT
                    w.wafer_id,
                    wi.cassette_id,
                    wi.slot_id,
                    l.label as location,
                    wi.arrived_at,
                    wi.sent_at
                FROM wafer_inventory wi
                JOIN wafers w ON wi.wafer_id = w.wafer_id
                JOIN locations l ON wi.location_id = l.location_id
            """
            )

            inventory = cursor.fetchall()

            # Convert datetime objects to ISO format for JSON serialization
            return jsonify(
                [
                    {
                        "wafer_id": item["wafer_id"],
                        "cassette_id": item["cassette_id"],
                        "slot_id": item["slot_id"],
                        "location": item["location"],
                        "arrived_at": (
                            item["arrived_at"].isoformat()
                            if item["arrived_at"]
                            else None
                        ),
                        "sent_at": (
                            item["sent_at"].isoformat() if item["sent_at"] else None
                        ),
                    }
                    for item in inventory
                ]
            )

    except Exception as e:
        logger.error(f"Error getting inventory API: {str(e)}")
        logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "status": "error",
                    "message": "Failed to retrieve inventory data",
                    "details": str(e) if app.debug else None,
                }
            ),
            500,
        )


def send_to_printer(printer_ip, pdf_file_path, copy_number):
    """Send a PDF file to a printer using socket connection

    Args:
        printer_ip (str): IP address of the printer
        pdf_file_path (str): Path to the PDF file to print
        copy_number (int): Number of copies to print

    Returns:
        bool or str: True if successful, error message string if failed
    """
    try:
        logger.info(f"Connecting to printer at IP: {printer_ip}")

        if not printer_ip:
            raise ValueError("Printer IP address is required")

        if not pdf_file_path or not os.path.exists(pdf_file_path):
            raise FileNotFoundError(f"PDF file not found at {pdf_file_path}")

        with open(pdf_file_path, "rb") as pdf_file:
            pdf_data = pdf_file.read()

        if not pdf_data:
            raise ValueError("PDF file is empty")

        logger.info(f"PDF file read, size: {len(pdf_data)} bytes")

        # Create and configure socket
        printer_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        printer_socket.settimeout(10)
        printer_socket.connect((printer_ip, 9100))
        logger.info("Connected to printer")

        # Send print job
        for i in range(copy_number):
            logger.info(f"Sending copy {i + 1} to printer")
            printer_socket.sendall(pdf_data)

        # Close connection
        printer_socket.close()
        logger.info("Print job sent successfully")
        return True

    except Exception as e:
        logger.error(f"Error in send_to_printer: {str(e)}")
        return str(e)


@app.route("/api/db_lots")
def get_lots_from_db():
    try:
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT DISTINCT w.lot_id
                FROM wafers w
                ORDER BY w.lot_id
            """
            )
            results = cursor.fetchall()
            return jsonify([row["lot_id"] for row in results])
    except Exception as e:
        logger.error(f"Error getting DB lots: {str(e)}")
        return jsonify({"error": str(e)}), 500


# This context processor makes auth functions available in all templates
@app.route("/")
@app.route("/dashboard")
@login_required
def home():
    """Render the dashboard/home page with statistics overview and analytics
    from both database and Asana
    """
    try:
        # Get inventory statistics from database with dynamic calculations
        inventory_stats = get_inventory_stats()
        app.logger.info(f"Inventory stats: {inventory_stats}")

        # Create a minimal asana_stats structure to satisfy the template
        # but use your dynamic data for all the metrics that matter
        asana_stats = {
            "total_shipments": inventory_stats.get(
                "shipped_wafers", 0
            ),  # Use dynamic data
            "on_time_rate": 95,  # Placeholder value
            "active_shipments": inventory_stats.get(
                "available_lots", 0
            ),  # Use dynamic data
        }

        # Create simple placeholder data for charts
        section_distribution = {"labels": [], "datasets": [{"data": []}]}
        monthly_deliveries = {"labels": [], "datasets": [{"data": []}]}
        recent_shipments = []

        # Format last updated time
        last_updated = datetime.now().strftime("%b %d, %Y, %I:%M %p")

        return render_template(
            "home.html",
            # Inventory stats - all using your dynamic data
            available_lots=inventory_stats.get("available_lots", 0),
            total_lots=inventory_stats.get("total_lots", 0),
            capacity_percentage=inventory_stats.get("capacity_percentage", 0),
            lot_change=inventory_stats.get("lot_change", 0),
            available_wafers=inventory_stats.get("available_wafers", 0),
            wafer_percentage=inventory_stats.get("wafer_percentage", 0),
            shipped_wafers=inventory_stats.get("shipped_wafers", 0),
            quarterly_target=inventory_stats.get("quarterly_target", 0),
            quarterly_progress=inventory_stats.get("quarterly_progress", 0),
            monthly_change=inventory_stats.get("monthly_change", 0),
            # Placeholder values for Asana-related template variables
            asana_stats=asana_stats,
            section_data=section_distribution,
            monthly_data=monthly_deliveries,
            recent_shipments=recent_shipments,
            last_updated=last_updated,
            datetime=datetime,
        )

    except Exception as e:
        app.logger.error(f"Error in home route: {str(e)}")
        # Handle the error appropriately
        return render_template("error.html", error=str(e))


@app.context_processor
def inject_auth_functions():
    def get_user_info():
        """Get current user information."""
        user_id = session.get("user_id")
        if user_id:
            # Get user from database to get the latest profile image
            from core.auth.user_manager import UserManager

            user = UserManager.get_user_by_id(user_id)
            if user:
                return {
                    "id": user["id"],
                    "email": user["email"],
                    "role": user["role"],
                    "name": user["name"],
                    "profile_image": user["profile_image"],
                    "last_login": user["last_login"],
                }

        # Fallback to session data if user not found
        return {
            "email": session.get("user_email", None),
            "role": session.get("user_role", None),
            "name": session.get("user_name", None),
            "profile_image": None,
            "last_login": None,
        }

    return {
        "check_permission": check_permission,
        "get_user_info": get_user_info,
        "user_is_authenticated": "user_email" in session,
    }


# Add default route for login page (if not handled by the blueprint)


@app.route("/login")
def login_redirect():
    return redirect(url_for("auth.login"))


@app.route("/register")
def register_redirect():
    return redirect(url_for("auth.register"))


@app.route("/health")
def health_redirect():
    """Health check endpoint for Docker/Swarm healthchecks"""
    try:
        # Check database connections
        db_status = "ok"
        auth_db_status = "ok"

        try:
            from database.db_config import test_connection

            db_result = test_connection()
            if not db_result:
                db_status = "error"
                app.logger.warning(
                    "Main database connection failed during health check"
                )
        except Exception as e:
            app.logger.error(f"Health check - Main DB error: {str(e)}")
            db_status = "error"

        try:
            from database.auth_db_config import test_auth_connection

            auth_result = test_auth_connection()
            if not auth_result:
                auth_db_status = "error"
                app.logger.warning(
                    "Auth database connection failed during health check"
                )
        except Exception as e:
            app.logger.error(f"Health check - Auth DB error: {str(e)}")
            auth_db_status = "error"

        # Get real system metrics
        from utils.health_check import get_health_metrics

        metrics = get_health_metrics()

        # Add database status to metrics
        metrics["database_status"] = db_status
        metrics["auth_database_status"] = auth_db_status

        overall_status = "healthy"
        if db_status == "error" or auth_db_status == "error":
            overall_status = "degraded"

        return jsonify({"status": overall_status, "data": metrics})
    except Exception as e:
        app.logger.error(f"Health check error: {str(e)}")
        return jsonify({"status": "unhealthy", "error": str(e)}), 500


def download_file_logic():
    total_steps = 100
    for step in range(1, total_steps + 1):
        time.sleep(0.05)  # Simulate download time
        progress = int((step / total_steps) * 100)
        message = f"Download {progress}% complete."
        socketio.emit("progress", {"progress": progress, "message": message})
    socketio.emit("progress", {"progress": 100, "message": "Download complete!"})


def handle_single_lot(selected_lot):
    """Process single lot selection."""
    try:
        session["current_lot"] = selected_lot
        return redirect(url_for("wafer_details", lot=selected_lot))
    except Exception as e:
        app.logger.error(f"Error processing single lot: {str(e)}")
        raise ValueError("Error processing lot selection. Please try again.")


def handle_multiple_lots(multiple_lots):
    """Process multiple lots selection."""
    lots_list = [lot.strip() for lot in multiple_lots.split(",") if lot.strip()]
    if not lots_list:
        raise ValueError("Please enter valid lot numbers separated by commas.")

    session["current_lot"] = multiple_lots
    return redirect(url_for("wafer_details", lot=multiple_lots))


def handle_asana_link(asana_link):
    """Process Asana link and retrieve task information."""
    app.logger.info(f"Processing Asana link: {asana_link}")
    task_gid = extract_task_gid(asana_link)
    app.logger.info(f"Extracted task_gid: {task_gid}")

    if not task_gid:
        raise ValueError("Invalid Asana link format")

    task_info = get_asana_task_info(task_gid)
    app.logger.info(f"Retrieved task info: {task_info}")

    if not task_info:
        raise ValueError("Failed to retrieve task information")

    session["current_task_gid"] = task_gid
    return render_template(
        "asana_task_info.html", task_info=task_info, csrf_token=generate_csrf()
    )


def initialize_form():
    """Initialize the lot selection form."""
    lots = get_all_lots()
    form = SelectLotForm()
    form.lot.choices = [("", "-- Select Lot --")] + [(lot, lot) for lot in lots]
    return form, lots


@app.route("/label-packing-slip", methods=["GET", "POST"])
def label_packing_slip():
    """Handle label and packing slip generation workflow."""
    try:
        form, _ = initialize_form()

        if request.method == "POST":
            app.logger.info(f"Processing POST request with data: {request.form}")

            form_data = {
                "selected_lot": request.form.get("lot", "").strip(),
                "multiple_lots": request.form.get("multiple_lots", "").strip(),
                "asana_link": request.form.get("asana_link", "").strip(),
            }

            try:
                if (
                    form_data["selected_lot"]
                    and form_data["selected_lot"] != "-- Select Lot --"
                ):
                    return handle_single_lot(form_data["selected_lot"])

                if form_data["multiple_lots"]:
                    return handle_multiple_lots(form_data["multiple_lots"])

                if form_data["asana_link"]:
                    return handle_asana_link(form_data["asana_link"])

                flash(
                    "Please select a lot, enter multiple lots, "
                    "or provide an Asana link.",
                    "error",
                )

            except ValueError as e:
                flash(str(e), "error")

        return render_template(
            "select_lot.html",
            form=form,
            csrf_token=generate_csrf(),
            lot=session.get("current_lot"),
        )

    except Exception as e:
        return handle_unexpected_error(e, form if "form" in locals() else None)


@app.route("/select_lot", methods=["GET", "POST"])
def select_lot():
    """Route to handle lot selection for wafer processing.
    Supports single lot selection, multiple lots, and Asana task links.
    """
    try:
        app.logger.debug("Entered the /select_lot route")
        form = initialize_lot_selection_form()

        if request.method == "POST":
            return handle_lot_selection_post(form)

        # Handle GET request
        app.logger.info("Processing GET request")
        return render_template(
            "select_lot.html",
            form=form,
            csrf_token=generate_csrf(),
            lot=session.get("current_lot"),
        )

    except Exception as e:
        return handle_selection_error(e, form if "form" in locals() else None)


def handle_lot_selection_post(form):
    """Process POST request for lot selection."""
    app.logger.info("Processing POST request")
    app.logger.info(f"Request form data: {request.form}")

    # Check if this is an AJAX request
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"

    # Extract form data
    selected_lot = request.form.get("lot", "").strip()
    multiple_lots = request.form.get("multiple_lots", "").strip()
    asana_link = request.form.get("asana_link", "").strip()

    app.logger.info(
        f"Parsed values - lot: {selected_lot}, multiple_lots: {multiple_lots}, "
        f"asana_link: {asana_link}"
    )

    # Process based on input type
    if selected_lot and selected_lot != "-- Select Lot --":
        return process_single_lot(selected_lot, is_ajax)
    elif multiple_lots:
        return process_multiple_lots(multiple_lots, is_ajax)
    elif asana_link:
        return process_asana_link(asana_link, is_ajax)
    else:
        return handle_no_selection(is_ajax, form)


def handle_selection_error(e, form=None):
    """Handle exceptions in the select_lot route."""
    app.logger.error(f"Error in select_lot route: {str(e)}")
    app.logger.error(traceback.format_exc())

    # If AJAX request, return JSON error
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return (
            jsonify(
                {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
            ),
            500,
        )

    flash(f"An unexpected error occurred: {str(e)}", "error")

    # Create a new form if one wasn't provided
    if not form:
        form = SelectLotForm()
        form.lot.choices = [("", "-- Select Lot --")]

    return render_template(
        "select_lot.html",
        form=form,
        csrf_token=generate_csrf(),
        lot=session.get("current_lot"),
    )


def initialize_lot_selection_form():
    """Initialize and populate the lot selection form."""
    try:
        lots = get_all_lots()
        app.logger.debug(f"Fetched lots from database: {lots}")

        form = SelectLotForm()
        form.lot.choices = [(lot, lot) for lot in lots]
        app.logger.debug("Form initialized and lot choices populated")

        return form
    except Exception as e:
        app.logger.error(f"Error initializing form: {str(e)}")
        app.logger.error(traceback.format_exc())
        # Return an empty form as fallback
        form = SelectLotForm()
        form.lot.choices = [("", "-- Select Lot --")]
        return form


def process_single_lot(selected_lot, is_ajax=False):
    """Process a single lot selection."""
    try:
        app.logger.info(f"Processing single lot selection: {selected_lot}")
        session["current_lot"] = selected_lot

        if is_ajax:
            return jsonify(
                {
                    "success": True,
                    "redirect": url_for("wafer_details", lot=selected_lot),
                }
            )

        return redirect(url_for("wafer_details", lot=selected_lot))
    except Exception as e:
        app.logger.error(f"Error processing single lot: {str(e)}")

        if is_ajax:
            return jsonify({"success": False, "message": str(e)}), 500

        flash("Error processing lot selection. Please try again.", "error")
        return redirect(url_for("select_lot"))


def handle_unexpected_error(error, form=None):
    """Handle unexpected errors in the label_packing_slip route."""
    app.logger.error(f"Error in label_packing_slip route: {str(error)}")
    app.logger.error(traceback.format_exc())

    if "database" in str(error).lower() or "connection" in str(error).lower():
        message = "Unable to access inventory data. Please try again later."
    else:
        message = f"An unexpected error occurred: {str(error)}"

    flash(message, "error")

    if not form:
        form = SelectLotForm()
        form.lot.choices = [("", "-- Select Lot --")]

    return render_template(
        "select_lot.html",
        form=form,
        csrf_token=generate_csrf(),
        lot=session.get("current_lot"),
    )


def validate_lots_list(lots_list, all_lots):
    """Validate that all lots in the list exist in the database."""
    invalid_lots = [lot for lot in lots_list if lot not in all_lots]
    if invalid_lots:
        raise ValueError(f"Invalid lot numbers found: {', '.join(invalid_lots)}")
    return True


def process_multiple_lots(multiple_lots, is_ajax=False):
    """Process multiple lots selection."""
    try:
        app.logger.info(f"Processing multiple lots: {multiple_lots}")
        lots_list = [lot.strip() for lot in multiple_lots.split(",") if lot.strip()]
        app.logger.debug(f"Parsed lots list: {lots_list}")

        if not lots_list:
            error_msg = "Please enter valid lot numbers separated by commas."

            if is_ajax:
                return jsonify({"success": False, "message": error_msg}), 400

            flash(error_msg, "error")
            return redirect(url_for("select_lot"))

        # Validate lots against database (moved inside try block)
        try:
            all_lots = set(get_all_lots())
            invalid_lots = [lot for lot in lots_list if lot not in all_lots]
            if invalid_lots:
                error_msg = f"Invalid lot numbers found: {', '.join(invalid_lots)}"

                if is_ajax:
                    return jsonify({"success": False, "message": error_msg}), 400

                flash(error_msg, "error")
                return redirect(url_for("select_lot"))
        except Exception as validation_error:
            app.logger.error(f"Error validating lots: {str(validation_error)}")
            # Continue even if validation fails - this is a non-critical error

        session["current_lot"] = multiple_lots

        if is_ajax:
            return jsonify(
                {
                    "success": True,
                    "redirect": url_for("wafer_details", lot=multiple_lots),
                }
            )

        return redirect(url_for("wafer_details", lot=multiple_lots))
    except Exception as e:
        app.logger.error(f"Error processing multiple lots: {str(e)}")

        if is_ajax:
            return jsonify({"success": False, "message": str(e)}), 500

        flash(
            "Error processing multiple lots. Please check the format and try again.",
            "error",
        )
        return redirect(url_for("select_lot"))


def process_asana_link(asana_link, is_ajax=False):
    """Process an Asana task link."""
    try:
        app.logger.info(f"Processing Asana link: {asana_link}")
        task_gid = extract_task_gid(asana_link)
        app.logger.debug(f"Extracted task_gid: {task_gid}")

        if not task_gid:
            error_msg = "Invalid Asana link format"

            if is_ajax:
                return jsonify({"success": False, "message": error_msg}), 400

            flash(error_msg, "error")
            return redirect(url_for("select_lot"))

        task_info = get_asana_task_info(task_gid)
        app.logger.debug(f"Retrieved task info: {task_info}")

        if not task_info:
            error_msg = "Failed to retrieve task information from Asana"

            if is_ajax:
                return jsonify({"success": False, "message": error_msg}), 400

            flash(error_msg, "error")
            return redirect(url_for("select_lot"))

        session["current_task_gid"] = task_gid

        if is_ajax:
            return jsonify(
                {
                    "success": True,
                    "redirect": url_for("asana_task_info", task_gid=task_gid),
                }
            )

        return redirect(url_for("asana_task_info", task_gid=task_gid))
    except Exception as e:
        app.logger.error(f"Error processing Asana link: {str(e)}")
        app.logger.error(traceback.format_exc())

        if is_ajax:
            return jsonify({"success": False, "message": str(e)}), 500

        flash(str(e), "error")
        return redirect(url_for("select_lot"))


def handle_no_selection(is_ajax, form):
    """Handle case when no valid selection is made."""
    app.logger.warning("No valid input provided in POST request")
    error_msg = "Please select a lot, enter multiple lots, or provide an Asana link."

    if is_ajax:
        return jsonify({"success": False, "message": error_msg}), 400

    flash(error_msg, "error")
    return render_template(
        "select_lot.html",
        form=form,
        csrf_token=generate_csrf(),
        lot=session.get("current_lot"),
    )


def create_lot_form():
    """Create and initialize the lot selection form."""
    lots = get_all_lots()
    form = SelectLotForm()
    form.lot.choices = [("", "-- Select Lot --")] + [(lot, lot) for lot in lots]
    return form


@app.route("/manual_selection", methods=["GET", "POST"])
def manual_selection():
    """Handle manual lot selection.
    GET: Display the selection form
    POST: Process the lot selection and redirect to wafer details
    """
    try:
        form = create_lot_form()

        if request.method == "POST":
            app.logger.info(f"Processing POST request with data: {request.form}")
            selected_lot = request.form.get("lot", "").strip()
            multiple_lots = request.form.get("multiple_lots", "").strip()

            try:
                # Handle single lot selection
                if selected_lot and selected_lot != "-- Select Lot --":
                    return process_single_lot_selection(selected_lot)

                # Handle multiple lots
                elif multiple_lots:
                    return process_multiple_lots_selection(multiple_lots)

                # Handle no selection
                else:
                    flash("Please select a lot or enter multiple lots.", "error")
                    return render_template("manual_selection.html", form=form)

            except ValueError as e:
                flash(str(e), "error")
                return render_template("manual_selection.html", form=form)

            except Exception as e:
                app.logger.error(f"Error processing selection: {str(e)}")
                app.logger.error(traceback.format_exc())
                flash("Error processing selection. Please try again.", "error")
                return render_template("manual_selection.html", form=form)

        # GET request - display the form
        return render_template("manual_selection.html", form=form)

    except Exception as e:
        app.logger.error(f"Error in manual selection: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash(f"An unexpected error occurred: {str(e)}", "error")

        # Ensure we always have a form to render
        if not form:
            form = SelectLotForm()
            form.lot.choices = [("", "-- Select Lot --")]

        return render_template("manual_selection.html", form=form)


def process_single_lot_selection(selected_lot):
    """Process a single lot selection and return appropriate redirect."""
    app.logger.info(f"Processing single lot selection: {selected_lot}")
    session["current_lot"] = selected_lot
    return redirect(url_for("wafer_details", lot=selected_lot))


def process_multiple_lots_selection(multiple_lots):
    """Process multiple lots selection and return appropriate redirect."""
    app.logger.info(f"Processing multiple lots: {multiple_lots}")
    lots_list = [lot.strip() for lot in multiple_lots.split(",") if lot.strip()]

    if not lots_list:
        raise ValueError("Please enter valid lot numbers separated by commas.")

    # Validate lots against database
    all_lots = set(get_all_lots())
    validate_lots_list(lots_list, all_lots)

    session["current_lot"] = multiple_lots
    return redirect(url_for("wafer_details", lot=multiple_lots))


@app.route("/process_wafer_selection", methods=["POST"])
def process_wafer_selection():
    """Handle wafer selection before moving to print labels"""
    try:
        app.logger.info(f"Processing wafer selection with data: {request.form}")

        # Extract form data
        wafer_pairs = request.form.get("wafer_pairs")
        slot_ids = request.form.get("slot_ids")
        scribe_ids = request.form.get("scribe_ids")
        current_lot = request.form.get("lot")

        app.logger.info(f"Wafer pairs: {wafer_pairs}")
        app.logger.info(f"Current lot: {current_lot}")

        if not wafer_pairs:
            return (
                jsonify(
                    {
                        "status": "error",
                        "message": "No wafers selected.",
                    }
                ),
                400,
            )

        # Store all necessary data in session
        paired_slots_wafers = [
            tuple(pair.split(",")) for pair in wafer_pairs.split(";") if pair
        ]
        session["paired_slots_wafers"] = paired_slots_wafers
        session["current_lot"] = current_lot

        if slot_ids:
            session["slot_ids"] = slot_ids
        if scribe_ids:
            session["scribe_ids"] = scribe_ids

        # Return success JSON response instead of redirect
        return jsonify(
            {
                "status": "success",
                "message": "Wafer selection processed successfully",
                "redirect": url_for("print_labels"),
            }
        )

    except Exception as e:
        app.logger.error(f"Error processing wafer selection: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"Error processing wafer selection: {str(e)}",
                }
            ),
            500,
        )


@app.route("/shipment_management", methods=["GET", "POST"])
def shipment_management():
    return render_template("shipment_management.html")


@app.route("/update_asana_task", methods=["POST"])
def update_asana_task():
    data = request.json
    task_gid = data.get("task_gid")
    field_name = data.get("field_name")
    new_value = data.get("new_value")

    try:
        configuration = asana.Configuration()
        configuration.access_token = os.getenv("ASANA_TOKEN")
        api_client = asana.ApiClient(configuration)
        tasks_api_instance = asana.TasksApi(api_client)

        # Add the required opts parameter for get_task
        get_opts = {
            "opt_pretty": True,
            "opt_fields": (
                "name,notes,due_on,completed,assignee,custom_fields,"
                "custom_fields.name,custom_fields.gid,custom_fields.type"
            ),
        }

        # First get the task with all custom fields
        task = tasks_api_instance.get_task(task_gid, opts=get_opts)
        app.logger.info(f"Full task response: {task}")

        # Update data based on field type
        field_update = {}

        if field_name in ["Task Name", "Task Description", "Due Date"]:
            # Handle standard fields
            field_map = {
                "Task Name": "name",
                "Task Description": "notes",
                "Due Date": "due_on",
            }
            field_update = {field_map[field_name]: new_value}
        else:
            # Get custom fields from task response
            custom_fields = task.get("custom_fields", [])
            app.logger.info(f"Available custom fields: {custom_fields}")

            # Find the matching custom field
            custom_field = next(
                (field for field in custom_fields if field["name"] == field_name), None
            )

            if custom_field:
                field_gid = custom_field["gid"]
                field_type = custom_field.get("type")

                # Format value based on field type
                if field_type == "number":
                    formatted_value = (
                        float(new_value) if "." in new_value else int(new_value)
                    )
                elif field_type == "text":
                    formatted_value = str(new_value)
                else:
                    formatted_value = new_value

                field_update = {"custom_fields": {field_gid: formatted_value}}
                app.logger.info(
                    f"Updating custom field {field_name} with value {formatted_value}"
                )
            else:
                app.logger.error(f"Custom field not found: {field_name}")
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": f"Custom field '{field_name}' not found",
                        }
                    ),
                    400,
                )

        # Wrap the update data in a "data" object as required by Asana API
        update_data = {"data": field_update}

        app.logger.info(f"Sending update with data: {update_data}")

        # Update the task
        tasks_api_instance.update_task(
            task_gid=task_gid, opts={"opt_pretty": True}, body=update_data
        )

        return jsonify({"success": True, "message": "Task updated successfully"})

    except Exception as e:
        app.logger.error(f"Error updating Asana task: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/wafer_details/<lot>")
def wafer_details(lot):
    try:
        app.logger.info(f"Fetching wafer details for lot: {lot}")

        # Use database query
        wafers = get_wafers_for_lot(lot)

        # Debug: log the results
        app.logger.info(f"Query returned {len(wafers)} wafers")
        if wafers:
            app.logger.info(f"First wafer keys: {list(wafers[0].keys())}")
            app.logger.info(f"Sample wafer: {wafers[0]}")
        else:
            app.logger.info("No wafers returned from query")

        return render_template("wafer_details.html", wafers=wafers, lot=lot)
    except Exception as e:
        app.logger.error(f"Error fetching wafer details: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash(f"Error fetching wafer details: {str(e)}", "error")
        return redirect(url_for("manual_selection"))


@app.route("/validate_wafers", methods=["POST"])
def validate_wafers_route():
    try:
        data = request.json
        app.logger.info(f"Validating wafers: {data}")
        wafer_ids = data.get("waferIds", [])

        if not wafer_ids:
            return jsonify({"success": False, "message": "No wafer IDs provided"}), 400

        # Use database validation
        validation_result = validate_wafers(wafer_ids)

        if not validation_result["all_valid"]:
            invalid_wafers = validation_result["invalid"]
            message = "The following wafers are not available at Ligentec FR:<br>"
            message += "<br>".join(
                [f"{wafer['wafer_id']} ({wafer['reason']})" for wafer in invalid_wafers]
            )
            message += "<br><br>Please contact support for assistance."
            return jsonify({"success": False, "message": message}), 400

        return jsonify(
            {"success": True, "message": "All wafers are available for printing"}
        )

    except Exception as e:
        app.logger.error(f"Error in validate_wafers: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
            ),
            500,
        )


def handle_ajax_get_request():
    """Handle AJAX GET request for form population."""
    app.logger.info("Processing AJAX GET request")
    task_info = {}

    if "current_task_gid" in session:
        task_gid = session["current_task_gid"]
        task_info = get_asana_task_info(task_gid) or {}
        app.logger.info(f"Retrieved task info: {task_info}")

        label_type = determine_label_type(task_info)
        app.logger.info(f"Determined label type: {label_type}")

    return jsonify(
        {
            "success": True,
            "data": {
                "label_title": session.get("asana_label_title", ""),
                "label_type": session.get("asana_label_type", "3"),
                "comments": session.get("asana_comments", ""),
                "wafer_count": len(session.get("paired_slots_wafers", [])),
                "po": session.get("asana_po", "N/A"),
                "project_id": session.get("asana_project_id", "N/A"),
                "lot_id": session.get("asana_xfab_lot_id", "N/A"),
                "shipment_origin": "france",
            },
        }
    )


def store_form_data(form):
    """Store form data in session."""
    session.update(
        {
            "shipment_date": form.shipment_date.data.strftime("%Y-%m-%d"),
            "title": form.label_title.data,
            "comments": form.comments.data,
            "wafer_count": form.manual_wafer_count.data
            or len(session.get("paired_slots_wafers", [])),
            "po": form.po.data,
            "project_id": form.project_id.data,
            "label_type": form.label_type.data,
            "xfab_lot_id": form.lot_id.data,
            "shipment_origin": form.shipment_origin.data,
            "printer_ip": form.printer_ip.data,
            "copy_number": form.copy_number.data,
        }
    )


def prepare_wafer_info():
    """Prepare wafer information for label generation."""
    paired_slots_wafers = session.get("paired_slots_wafers", [])
    wafer_info = [
        {"slot": slot_id, "wafer_id": wafer_id}
        for slot_id, wafer_id in paired_slots_wafers
    ]
    session["wafer_info"] = wafer_info
    return wafer_info


def generate_files():
    """Generate label and packing slip files."""
    app.logger.info("Generating label file...")
    label_file_path = generate_label(session)
    app.logger.info(f"Label file generated at: {label_file_path}")

    app.logger.info("Generating packing slip...")
    packing_slip_file_path = generate_packing_slip(session)
    app.logger.info(f"Packing slip generated at: {packing_slip_file_path}")

    session["label_file_path"] = label_file_path
    session["packing_slip_file_path"] = packing_slip_file_path

    return jsonify(
        {
            "status": "success",
            "message": "Files generated successfully",
            "redirect": url_for("download_files"),
        }
    )


@app.route("/print_labels", methods=["GET", "POST"])
def print_labels():
    """Handle the print labels form and label generation process."""
    try:
        form = PrintLabelForm()
        lot = session.get("current_lot")

        if (
            request.method == "GET"
            and request.headers.get("X-Requested-With") == "XMLHttpRequest"
        ):
            return handle_ajax_get_request()

        if request.method == "POST":
            return handle_print_labels_post(form)

        return render_template(
            "print_labels.html", form=form, lot=lot, csrf_token=generate_csrf()
        )

    except Exception as e:
        return handle_print_labels_error(e, form, lot)


def handle_print_labels_post(form):
    """Process POST request for print_labels."""
    if not form.validate_on_submit():
        return (
            jsonify(
                {
                    "status": "error",
                    "message": "Form validation failed",
                    "errors": form.errors,
                }
            ),
            400,
        )

    try:
        store_form_data(form)
        process_new_slot_ids()
        prepare_wafer_info()
        return generate_files()
    except Exception as e:
        app.logger.error(f"Error generating files: {str(e)}")
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"Error generating files: {str(e)}",
                }
            ),
            500,
        )


def process_new_slot_ids():
    """Process and store new slot IDs from form data."""
    new_slot_ids_json = request.form.get("new_slot_ids")
    if not new_slot_ids_json:
        return

    try:
        new_slot_ids = json.loads(new_slot_ids_json)
        session["new_slot_ids"] = new_slot_ids
        app.logger.info(f"Stored new slot IDs: {new_slot_ids}")
    except json.JSONDecodeError:
        app.logger.error(f"Failed to parse new slot IDs: {new_slot_ids_json}")


def handle_print_labels_error(error, form, lot):
    """Handle errors in print_labels route."""
    app.logger.error(f"Error in print_labels route: {str(error)}")
    app.logger.error(traceback.format_exc())

    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return (
            jsonify(
                {
                    "status": "error",
                    "message": f"An unexpected error occurred: {str(error)}",
                }
            ),
            500,
        )

    flash(f"An error occurred: {str(error)}", "error")
    return render_template(
        "print_labels.html", form=form, lot=lot, csrf_token=generate_csrf()
    )


@app.route("/download_label")
def download_label_route():
    try:
        label_file_path = session.get("label_file_path")
        if not label_file_path or not os.path.exists(label_file_path):
            # If no file path in session or file doesn't exist, generate a new one
            label_file_path = download_label(generate_only=True)
            session["label_file_path"] = label_file_path

        if not label_file_path or not os.path.exists(label_file_path):
            app.logger.error("Label file could not be generated or found")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Label file could not be generated or found",
                    }
                ),
                404,
            )

        app.logger.info(f"Sending label file: {label_file_path}")

        # Create response with explicit MIME type and Content-Disposition header
        response = make_response(
            send_file(
                label_file_path,
                as_attachment=True,
                download_name=os.path.basename(label_file_path),
                mimetype="application/pdf",
            )
        )

        # Set explicit Content-Disposition header to ensure proper download
        filename = os.path.basename(label_file_path)
        response.headers["Content-Disposition"] = f'attachment; filename="{filename}"'
        response.headers["Content-Type"] = "application/pdf"

        return response

    except Exception as e:
        app.logger.error(f"Error in download_label_route: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "success": False,
                    "message": (
                        f"An error occurred while downloading the label: " f"{str(e)}"
                    ),
                }
            ),
            500,
        )


def verify_inventory_contents():
    """Verify and print the contents of the inventory database for debugging purposes."""
    try:
        with get_db_cursor() as cursor:
            cursor.execute(
                """
                SELECT COUNT(*) as total_rows FROM wafer_inventory
            """
            )
            count = cursor.fetchone()["total_rows"]

            cursor.execute(
                """
                SELECT column_name FROM information_schema.columns
                WHERE table_name = 'wafer_inventory'
            """
            )
            columns = [row["column_name"] for row in cursor.fetchall()]

            print("Current inventory database contents:")
            print(f"Total rows: {count}")
            print(f"Columns: {columns}")

    except Exception as e:
        print(f"Error verifying inventory: {e}")


def get_label_data_from_session():
    """Extract label data from the session."""
    data = {
        "paired_slots_wafers": session.get("paired_slots_wafers", []),
        "new_slot_ids": session.get("new_slot_ids", []),
        "label_type": session.get("label_type"),
        "shipment_date": session.get("shipment_date"),
        "label_title": session.get("title", ""),
        "wafer_count": session.get("wafer_count"),
        "po": session.get("po"),
        "project_id": session.get("project_id"),
        "xfab_lot_id": session.get("xfab_lot_id"),
    }

    # Add substrate wafer specific fields if this is a substrate wafer label
    if session.get("label_type") == "4":
        data["item_id"] = session.get("fs_item_id", "")
        data["svm_lot_id"] = session.get("fs_svm_lot_id", "")

    return data


def create_label_file_path(label_title):
    """Create a file path for the label PDF."""
    sanitized_title = sanitize_filename(label_title) if label_title else "label"
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return os.path.join(DOWNLOAD_FOLDER, f"{sanitized_title}_{timestamp}_label.pdf")


def generate_wafer_list(paired_slots_wafers, new_slot_ids):
    """Generate a list of wafer entries for the label."""
    return [
        f"{new_slot},{scribe_id}"
        for (_, scribe_id), new_slot in zip(
            paired_slots_wafers, new_slot_ids, strict=False
        )
    ]


def generate_qr_code(wafer_list):
    """Generate a QR code image from the wafer list."""
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=10,
        border=4,
    )
    qr.add_data("\n".join(wafer_list))
    qr.make(fit=True)
    return qr.make_image(fill_color="black", back_color="white")


def get_label_font():
    """Get a font for the label text that works across platforms."""
    try:
        # Check for environment-specified font path
        env_font_path = os.environ.get("DEJAVU_FONT_PATH")
        if env_font_path and os.path.exists(env_font_path):
            app.logger.info(f"Loading font from environment path: {env_font_path}")
            return ImageFont.truetype(env_font_path, 30)

        # Try several known DejaVu font locations
        dejavu_paths = [
            "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf",  # Alpine path
            "/usr/share/fonts/dejavu/DejaVuSans.ttf",  # Symlinked path
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Debian/Ubuntu path
            "/usr/share/fonts/TTF/DejaVuSans.ttf",  # Possible path on some systems
        ]

        for path in dejavu_paths:
            if os.path.exists(path):
                app.logger.info(f"Loading DejaVu Sans font from {path}")
                return ImageFont.truetype(path, 30)

        # Try system font path
        try:
            font_path = get_system_font_path()
            app.logger.info(f"Loading system font from {font_path}")
            return ImageFont.truetype(font_path, 30)
        except Exception as e:
            app.logger.error(f"Error loading font via system path: {str(e)}")

        # Use default PIL font
        app.logger.warning("Using default PIL font as fallback")
        return ImageFont.load_default()

    except Exception as e:
        app.logger.error(f"Error in get_label_font: {str(e)}")
        # Always return a usable font even if there's an error
        return ImageFont.load_default()


def create_label_text(data, wafer_list):
    """Create the text content for the label."""
    text = ""

    # Skip label title for Substrate-wafer labels
    if data["label_type"] != "4":
        text = f"Label Title: {data['label_title']}\n\n"

    text += f"Date: {data['shipment_date']}\n\n"
    text += f"Wafers number: {data['wafer_count']}\n\n"

    # Only add Project ID, PO, and X-FAB Lot ID for Erfurt label
    if data["label_type"] == "2":
        text += f"Purchase order: {data['po']}\n\n"
        text += f"Project ID: {data['project_id']}\n\n"
        text += f"X-FAB Lot ID: {data['xfab_lot_id']}\n\n"

    # Add Substrate-wafer specific fields
    if data["label_type"] == "4":
        if "item_id" in data and data["item_id"]:
            text += f"Item ID: {data['item_id']}\n\n"
        if "svm_lot_id" in data and data["svm_lot_id"]:
            text += f"SVM lot ID: {data['svm_lot_id']}\n\n"

    # Add wafer list (skip for Substrate-wafer)
    if data["label_type"] != "4":
        text += "Slot ID,Wafer ID\n\n"
        for i in range(0, len(wafer_list), 2):
            line = f"{wafer_list[i]}  "
            if i + 1 < len(wafer_list):
                line += f"{wafer_list[i + 1]}\n"
            else:
                line += "\n"
            text += line

    return text


def create_label_image(data, wafer_list, qr_img):
    """Create the label image with text and QR code."""
    # Create label image
    img_width, img_height = int(9 * 28.3465 * 3), int(15 * 28.3465 * 3)
    img = Image.new("RGB", (img_width, img_height), color="white")
    draw = ImageDraw.Draw(img)

    # Add logo if not label type 1
    if data["label_type"] != "1":
        logo_path = GENERAL_LOGO_PATH
        if os.path.exists(logo_path):
            logo = Image.open(logo_path)
            img.paste(logo, (50, 50))

    # Get font and set up text
    font = get_label_font()
    text_y = 200 if data["label_type"] != "1" else 100
    text_to_write = create_label_text(data, wafer_list)

    # Draw text on image
    draw.text((70, text_y), text_to_write, fill=(0, 0, 0), font=font)

    # Add QR code
    qr_size = int(11 * 28.3465)
    qr_img_resized = qr_img.resize((qr_size, qr_size))
    img.paste(qr_img_resized, (420, 950))

    return img, img_width, img_height


def save_label_as_pdf(img, img_width, img_height, label_file_path):
    """Save the label image as a PDF file."""
    c = canvas.Canvas(label_file_path, pagesize=(img_width / 3, img_height / 3))
    c.drawImage(ImageReader(img), 0, 0, width=img_width / 3, height=img_height / 3)
    c.showPage()
    c.save()
    return label_file_path


def download_label(generate_only=False):
    """Generate and download a label PDF based on session data."""
    try:
        app.logger.info(
            f"Starting download_label function. Generate only: {generate_only}"
        )
        app.logger.info(f"Session data in download_label: {session}")

        # Get data from session
        data = get_label_data_from_session()

        # Create file path
        label_file_path = create_label_file_path(data["label_title"])

        # Generate wafer list and QR code
        wafer_list = generate_wafer_list(
            data["paired_slots_wafers"], data["new_slot_ids"]
        )
        qr_img = generate_qr_code(wafer_list)

        # Create and save the label image
        img, img_width, img_height = create_label_image(data, wafer_list, qr_img)
        save_label_as_pdf(img, img_width, img_height, label_file_path)

        app.logger.info(f"Label PDF saved to: {label_file_path}")

        # Always return the file path, regardless of generate_only parameter
        # This ensures we never return a Response object
        # that could be stored in the session
        return label_file_path

    except Exception as e:
        app.logger.error(f"Error in download_label: {str(e)}")
        app.logger.error(traceback.format_exc())
        raise


@app.route("/freestyle", methods=["POST"])
@csrf.exempt  # Exempt this route from CSRF protection to fix form submission issues
def freestyle():
    """Handle free style form submission from the select_lot page.
    Creates a session with the free style data for label generation.
    For Substrate-wafer labels, skips the print_labels form and goes
    directly to download_files.
    """
    app.logger.info("Free style endpoint called")
    try:
        # Extract form data
        item_id = request.form.get("fs_item_id", "").strip()
        wafer_count = request.form.get("fs_wafer_count", "").strip()
        svm_lot_id = request.form.get("fs_svm_lot_id", "").strip()
        comments = request.form.get("fs_comments", "").strip()

        app.logger.info(
            f"Free style data - Item ID: {item_id}, Wafer count: {wafer_count}, "
            f"SVM lot ID: {svm_lot_id}, Comments: {comments}"
        )

        # Validate required fields
        if not item_id:
            return jsonify({"success": False, "message": "Item ID is required"}), 400
        if not wafer_count:
            return (
                jsonify({"success": False, "message": "Wafer count is required"}),
                400,
            )
        if not svm_lot_id:
            return jsonify({"success": False, "message": "SVM lot ID is required"}), 400

        # Store data in session
        session["freestyle_mode"] = True
        session["fs_item_id"] = item_id
        session["fs_wafer_count"] = wafer_count
        session["fs_svm_lot_id"] = svm_lot_id
        session["fs_comments"] = comments

        # Set label type to Substrate-wafer (4)
        session["label_type"] = "4"
        session["asana_label_type"] = "4"

        # Create a dummy paired_slots_wafers entry for compatibility
        # This will be ignored in the actual label generation
        session["paired_slots_wafers"] = [("1", "FREESTYLE")]

        # Set label title to item ID
        session["title"] = item_id
        session["label_title"] = item_id
        session["asana_label_title"] = item_id

        # Set today's date as shipment date
        today = datetime.now().strftime("%Y-%m-%d")
        session["shipment_date"] = today

        # Set default values for other required fields
        session["wafer_count"] = int(wafer_count) if wafer_count.isdigit() else 0
        session["comments"] = comments
        session["new_slot_ids"] = ["1"]  # Dummy slot ID

        # Generate the label file directly
        try:
            # Generate label file
            label_file_path = download_label(generate_only=True)
            session["label_file_path"] = label_file_path
            app.logger.info(f"Generated label file: {label_file_path}")

            # For Substrate-wafer labels, we don't need a packing slip
            if session.get("label_type") != "4":
                # Generate packing slip file for non-Substrate-wafer labels
                packing_slip_file_path = generate_packing_slip(session, origin="france")
                session["packing_slip_file_path"] = packing_slip_file_path
                app.logger.info(
                    f"Generated packing slip file: {packing_slip_file_path}"
                )
            else:
                app.logger.info(
                    "Skipping packing slip generation for Substrate-wafer label"
                )
                # Create a dummy packing slip path to satisfy the download_files route check
                # This is needed because download_files checks for both label_file_path and packing_slip_file_path
                dummy_path = os.path.join(
                    DOWNLOAD_FOLDER,
                    f"dummy_packing_slip_{datetime.now().strftime('%Y%m%d%H%M%S')}.pdf",
                )
                session["packing_slip_file_path"] = dummy_path
                app.logger.info(f"Set dummy packing slip path: {dummy_path}")
        except Exception as gen_error:
            app.logger.error(f"Error generating files: {str(gen_error)}")
            app.logger.error(traceback.format_exc())
            raise ValueError(f"Error generating files: {str(gen_error)}")

        # Check if it's an AJAX request
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        if is_ajax:
            return jsonify(
                {
                    "success": True,
                    "message": "Free style data processed successfully",
                    "redirect": url_for("download_files"),
                }
            )
        else:
            return redirect(url_for("download_files"))

    except Exception as e:
        app.logger.error(f"Error processing free style data: {str(e)}")
        app.logger.error(traceback.format_exc())

        # Check if it's an AJAX request
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        if is_ajax:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": f"Error processing free style data: {str(e)}",
                    }
                ),
                500,
            )
        else:
            flash(f"Error processing free style data: {str(e)}", "error")
            return redirect(url_for("select_lot"))


def generate_packing_slip(session_data, origin=None, generate_only=False):
    """Generate packing slip based on session data.

    Args:
        session_data: Dictionary containing session data
        origin: Optional origin location (france or switzerland)
        generate_only: If True, only generate the file and return the path
                      without sending it as a response (deprecated,
                      kept for compatibility)

    Returns:
        Always returns the file path to avoid storing Response objects in the session
    """
    try:
        app.logger.info("Generating packing slip with session data")

        # Create a copy of the session data
        session_data_copy = dict(session_data)

        # Set origin if provided
        if origin:
            session_data_copy["shipment_origin"] = origin

        # Extract data from session
        data = extract_packing_slip_data(session_data_copy)

        # Create file path
        packing_slip_file_path = create_packing_slip_file_path(data["label_title"])

        # Initialize PDF
        c = initialize_packing_slip_pdf(packing_slip_file_path)

        # Draw content on PDF
        draw_packing_slip_content(c, data)

        # Save and return
        c.save()

        # Always return the file path, regardless of generate_only parameter
        # This ensures we never return a Response object
        # that could be stored in the session;
        return packing_slip_file_path

    except Exception as e:
        app.logger.error(f"Error generating packing slip: {str(e)}")
        app.logger.error(traceback.format_exc())
        raise


def extract_packing_slip_data(session_data):
    """Extract required data from session for packing slip."""
    # Get original data
    paired_slots_wafers = session_data.get("paired_slots_wafers", [])
    new_slot_ids = session_data.get("new_slot_ids", [])

    # Replace original slot IDs with new ones if available
    if new_slot_ids and len(new_slot_ids) == len(paired_slots_wafers):
        paired_slots_wafers = [
            (new_slot_ids[i], wafer_id)
            for i, (_, wafer_id) in enumerate(paired_slots_wafers)
        ]

    return {
        "paired_slots_wafers": paired_slots_wafers,
        "shipment_date": session_data.get("shipment_date"),
        "label_title": session_data.get("title", ""),
        "wafer_count": session_data.get("wafer_count"),
        "po": session_data.get("po"),
        "project_id": session_data.get("project_id"),
        "comments": session_data.get("comments", "").strip(),
        "xfab_lot_id": session_data.get("xfab_lot_id"),
        "shipment_origin": session_data.get("shipment_origin", "france"),
        "label_type": session_data.get("label_type"),
        "contact_person": session_data.get("contact_person", ""),
    }


def create_packing_slip_file_path(label_title):
    """Create file path for the packing slip."""
    sanitized_title = sanitize_filename(label_title) if label_title else "packing_slip"
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    return os.path.join(
        DOWNLOAD_FOLDER, f"{sanitized_title}_{timestamp}_packing_slip.pdf"
    )


def initialize_packing_slip_pdf(file_path):
    """Initialize PDF canvas with A4 size."""
    return canvas.Canvas(file_path, pagesize=A4)


def draw_packing_slip_content(c, data):
    """Draw all content on the packing slip PDF."""
    width, height = A4

    # Get company address based on origin
    company_address = get_company_address(data["shipment_origin"])
    y_position = height - 50

    # Draw logo if not label type 1
    if data["label_type"] != "1":
        draw_logo(c, width, height)

        # Add company address
        y_position = draw_company_address(c, company_address, height)

    # Add title
    y_position = draw_packing_slip_title(c, width, y_position)

    # Draw fields based on label type
    y_position = draw_packing_slip_fields(c, data, y_position)

    # Draw wafer pairs
    y_position = draw_wafer_pairs(c, data["paired_slots_wafers"], y_position)

    # Add comments if present
    if data["comments"]:
        y_position = draw_comments(c, data["comments"], width, y_position)

    # Add QR code
    draw_qr_code(c, data["paired_slots_wafers"], width)


def get_company_address(shipment_origin):
    """Get company address based on shipment origin."""
    france_address = [
        "LIGENTEC France SAS",
        "224 Boulevard John Kennedy",
        "91100 Corbeil-Essonnes",
        "France",
    ]

    switzerland_address = [
        "LIGENTEC SA",
        "CH DE LA DENT D'OCHE 1B EPFL INNOVATION PARK",
        "BATIMENT L",
        "1024 ECUBLENS VD",
        "SWITZERLAND",
    ]

    return france_address if shipment_origin == "france" else switzerland_address


def draw_logo(c, width, height):
    """Draw logo on the packing slip."""
    logo_path = GENERAL_LOGO_PATH
    if os.path.exists(logo_path):
        c.drawImage(
            logo_path, 50, height - 80, width=120, height=80, preserveAspectRatio=True
        )


def draw_company_address(c, address, height):
    """Draw company address on the packing slip."""
    # Use environment-forced font if available
    font = os.environ.get("FORCE_FONT", "Helvetica")
    safe_font = get_safe_font_name(font)
    c.setFont(safe_font, 10)

    y_position = height - 110
    for line in address:
        c.drawString(50, y_position, line)
        y_position -= 15

    # Add extra space after address
    return y_position - 20


def draw_packing_slip_title(c, width, y_position):
    """Draws the title section of the packing slip.
    Returns the updated y_position.
    """
    # Use environment-forced font if available
    font = os.environ.get("FORCE_FONT", "Helvetica")
    app.logger.info(f"Using environment-forced font: {font}")

    safe_font = get_safe_font_name(font)

    # Draw title
    c.setFont(safe_font, 24)
    c.drawCentredString(width / 2, y_position, "PACKING SLIP")
    y_position -= 30

    return y_position


def draw_packing_slip_fields(c, data, y_position):
    """Draw the required fields based on label type."""
    # Use environment-forced font if available
    font = os.environ.get("FORCE_FONT", "Helvetica")
    safe_font = get_safe_font_name(font)
    c.setFont(safe_font, 12)
    y_position = y_position - 60  # Additional spacing before fields
    line_height = 25

    # Add Attn To field (will be displayed for all label types)
    if data.get("contact_person"):
        c.drawString(50, y_position, f"Attn To: {data['contact_person']}")
        y_position -= line_height

    # Draw the required fields based on label type
    if data["label_type"] == "2":  # Erfurt label
        fields = [
            ("Purchase order", data["po"]),
            ("Project ID", data["project_id"]),
            ("XFAB Lot ID", data["xfab_lot_id"]),
            ("Wafers number", str(data["wafer_count"])),
            ("Date", data["shipment_date"]),
        ]
    else:
        fields = [
            ("Date", data["shipment_date"]),
            ("Label Title", data["label_title"]),
            ("Wafers number", str(data["wafer_count"])),
        ]

    # Draw fields
    for field_name, value in fields:
        if value and value != "N/A":
            c.drawString(50, y_position, f"{field_name} : {value}")
            y_position -= line_height

    return y_position - line_height * 0.5


def draw_wafer_pairs(c, paired_slots_wafers, y_position):
    """Draw wafer pair information on the packing slip."""
    if not paired_slots_wafers:
        return y_position

    c.drawString(50, y_position, "Slot ID,Wafer ID")
    y_position -= 25

    current_line = []
    for i, (slot_id, wafer_id) in enumerate(paired_slots_wafers, 1):
        current_line.append(f"{slot_id},{wafer_id}")

        if i % 4 == 0 or i == len(paired_slots_wafers):
            wafer_line = "    ".join(current_line)
            c.drawString(50, y_position, wafer_line)
            y_position -= 20
            current_line = []

    return y_position - 25


def draw_comments(c, comments, width, y_position):
    """Draw comments section on the packing slip."""
    y_position -= 15  # Extra space before comments

    # Use environment-forced font if available
    font = os.environ.get("FORCE_FONT", "Helvetica")
    safe_font = get_safe_font_name(font)

    # Set font for comments header
    c.setFont(safe_font, 12)
    c.drawString(50, y_position, "Comments:")
    y_position -= 25

    # Wrap comments text with the new function
    wrapped_comments = wrap_text(comments, font, 12, width - 100)
    for line in wrapped_comments:
        c.drawString(50, y_position, line)
        y_position -= 20

    return y_position


def draw_qr_code(c, paired_slots_wafers, width):
    """Generate and draw QR code on the packing slip."""
    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    wafer_list = [f"{slot_id},{wafer_id}" for slot_id, wafer_id in paired_slots_wafers]
    qr.add_data("\n".join(wafer_list))
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")

    qr_buffer = io.BytesIO()
    qr_img.save(qr_buffer, format="PNG")
    qr_buffer.seek(0)

    # Add QR code
    qr_size = 100
    c.drawImage(
        ImageReader(qr_buffer), width - qr_size - 50, 100, width=qr_size, height=qr_size
    )


def wrap_text(text, font, font_size, max_width):
    """Wraps text to fit within a specified width.
    Returns a list of lines.
    """
    if not text:
        return []

    try:
        app.logger.info(f"Text wrapping using font: '{font}'")

        # Get a guaranteed available font
        safe_font = get_safe_font_name(font)
        app.logger.info(f"Using safe font for text wrapping: {safe_font}")

        # Create a dummy canvas to measure text
        packet = io.BytesIO()
        c = canvas.Canvas(packet)

        try:
            c.setFont(safe_font, font_size)
        except Exception as font_error:
            app.logger.error(f"Error setting font {safe_font}: {str(font_error)}")
            # If we can't set the requested font, use Helvetica.
            # Helvetica is always available.
            safe_font = "Helvetica"
            c.setFont(safe_font, font_size)

        words = text.split()
        lines = []
        current_line = []

        for word in words:
            test_line = " ".join(current_line + [word])
            try:
                width = c.stringWidth(test_line)

                if width <= max_width:
                    current_line.append(word)
                else:
                    if current_line:
                        lines.append(" ".join(current_line))
                        current_line = [word]
                    else:
                        # Word is too long for the line, need to split it
                        lines.append(word)
                        current_line = []
            except Exception as width_error:
                app.logger.error(f"Error measuring text width: {str(width_error)}")
                # If we can't measure width, just add the word and move on
                current_line.append(word)

                # If the line is getting long, start a new one
                if len(current_line) > 10:  # Arbitrary threshold
                    lines.append(" ".join(current_line))
                    current_line = []

        if current_line:
            lines.append(" ".join(current_line))

        return lines
    except Exception as e:
        app.logger.error(f"Error in text wrapping with font {font}: {str(e)}")
        # Fallback to simple wrapping without font metrics
        # Split text into chunks of approximately 50 characters
        chunks = []
        for i in range(0, len(text), 50):
            chunks.append(text[i : i + 50])
        return chunks if chunks else [text]


def format_wafer_list(paired_slots_wafers):
    wafer_list = []
    for i in range(0, len(paired_slots_wafers), 2):
        pair = paired_slots_wafers[i : i + 2]
        formatted_pair = " ".join(f"{slot},{scribe}" for slot, scribe in pair)
        wafer_list.append(formatted_pair)
    return wafer_list


@app.route("/download_packing_slip")
def download_packing_slip():
    try:
        # Get the origin from query parameter or default to 'france'
        origin = request.args.get("origin", "france")
        if origin not in ["france", "switzerland"]:
            origin = "france"  # Default to France if invalid value

        # Check if we already have a generated packing slip file
        packing_slip_file_path = session.get("packing_slip_file_path")

        # If no file path in session or file doesn't exist, generate a new one
        if not packing_slip_file_path or not os.path.exists(packing_slip_file_path):
            # Generate the packing slip with the specified origin
            packing_slip_file_path = generate_packing_slip(session, origin=origin)
            session["packing_slip_file_path"] = packing_slip_file_path

        if not packing_slip_file_path or not os.path.exists(packing_slip_file_path):
            app.logger.error("Packing slip file could not be generated or found")
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Packing slip file could not be generated or found",
                    }
                ),
                404,
            )

        app.logger.info(f"Sending packing slip file: {packing_slip_file_path}")

        # Create response with explicit MIME type and Content-Disposition header
        response = make_response(
            send_file(
                packing_slip_file_path,
                as_attachment=True,
                download_name=os.path.basename(packing_slip_file_path),
                mimetype="application/pdf",
            )
        )

        # Set explicit Content-Disposition header to ensure proper download
        filename = os.path.basename(packing_slip_file_path)
        response.headers["Content-Disposition"] = f'attachment; filename="{filename}"'
        response.headers["Content-Type"] = "application/pdf"

        return response

    except Exception as e:
        app.logger.error(
            f"An error occurred in download_packing_slip: {str(e)}\n"
            f"{traceback.format_exc()}"
        )
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"An unexpected error occurred: {str(e)}.Try again.",
                }
            ),
            500,
        )


@app.route("/download_files")
def download_files():
    app.logger.info("Download files route accessed")
    print("Download files route accessed")  # This will show in terminal

    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return jsonify(
            {"success": True, "message": "Download files route accessed via AJAX"}
        )
    try:
        app.logger.info("Entering download_files route")
        app.logger.info(f"Session contents: {session}")

        label_file_path = session.get("label_file_path")
        packing_slip_file_path = session.get("packing_slip_file_path")

        app.logger.info(f"Label file path: {label_file_path}")
        app.logger.info(f"Packing slip file path: {packing_slip_file_path}")

        # Check for file paths in session
        if not label_file_path or not packing_slip_file_path:
            app.logger.error("Missing file paths in session")
            app.logger.error(f"label_file_path exists: {bool(label_file_path)}")
            app.logger.error(
                f"packing_slip_file_path exists: {bool(packing_slip_file_path)}"
            )
            flash(
                "Files not found. Please generate the label and packing slip first.",
                "error",
            )
            return redirect(url_for("print_labels"))

        # Check if files exist on disk
        label_file_exists = os.path.exists(label_file_path)

        # For substrate wafer labels (label_type = 4), we don't need to check if the packing slip exists
        # because we're using a dummy path that doesn't actually exist on disk
        is_substrate_wafer = session.get("label_type") == "4"
        packing_slip_check = (
            True if is_substrate_wafer else os.path.exists(packing_slip_file_path)
        )

        if not label_file_exists or not packing_slip_check:
            app.logger.error("Generated files not found on disk")
            app.logger.error(f"label_file exists: {label_file_exists}")
            app.logger.error(f"is_substrate_wafer: {is_substrate_wafer}")
            app.logger.error(f"packing_slip exists: {packing_slip_check}")
            flash("Generated files not found. Please try again.", "error")
            return redirect(url_for("print_labels"))

        # Calculate dates
        today_date = datetime.now().strftime("%Y-%m-%d")
        expected_received_date = adjust_date_for_holidays_and_weekends(
            datetime.now() + timedelta(days=3)
        ).strftime("%Y-%m-%d")

        # Get task info
        task_info = {}
        if "current_task_gid" in session:
            task_gid = session["current_task_gid"]
            app.logger.info(f"Found task_gid in session: {task_gid}")
            task_info = get_asana_task_info(task_gid) or {}
            app.logger.info(f"Retrieved task info: {task_info}")

        app.logger.info("About to render download_files.html template")
        return render_template(
            "download_files.html",
            today_date=today_date,
            expected_received_date=expected_received_date,
            task_info=task_info,
            current_lot=session.get("current_lot", ""),
            paired_slots_wafers=session.get("paired_slots_wafers", []),
            update_type="asana" if "current_task_gid" in session else "manual",
        )

    except Exception as e:
        app.logger.error(f"Error in download_files: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash("An error occurred while preparing the download page.", "error")
        return redirect(url_for("print_labels"))


@app.route("/shipment_statistics")
def shipment_statistics():
    try:
        # Get date range parameters
        range_type = request.args.get("range", "30")
        start_date = request.args.get("start")
        end_date = request.args.get("end")

        # Calculate date range
        today = datetime.now()
        if range_type == "custom" and start_date and end_date:
            start_date = datetime.strptime(start_date, "%Y-%m-%d")
            end_date = datetime.strptime(end_date, "%Y-%m-%d")
        else:
            days = int(range_type)
            start_date = today - timedelta(days=days)
            end_date = today

        project_gid = "1206397258493005"
        stats = get_shipment_stats_from_asana(project_gid)

        if stats:
            # Define sections
            sections = [
                "Incoming",
                "Planned",
                "In preparation",
                "packaged",
                "Waiting for review",
                "Sent",
                "Delivered",
            ]

            # Filter data based on date range
            filtered_stats = filter_stats_by_date(stats, start_date, end_date)

            # Prepare data for Chart.js
            section_data = {
                "labels": sections,
                "datasets": [
                    {
                        "label": "Tasks per Section",
                        "data": [
                            filtered_stats["sections"].get(section, 0)
                            for section in sections
                        ],
                        "backgroundColor": [
                            "#4B5563",
                            "#60A5FA",
                            "#34D399",
                            "#F59E0B",
                            "#EC4899",
                            "#8B5CF6",
                            "#10B981",
                        ],
                    }
                ],
            }

            monthly_data = {
                "labels": list(filtered_stats["monthly_deliveries"].keys()),
                "datasets": [
                    {
                        "label": "Monthly Deliveries",
                        "data": list(filtered_stats["monthly_deliveries"].values()),
                        "borderColor": "#3B82F6",
                        "backgroundColor": "rgba(59, 130, 246, 0.1)",
                        "tension": 0.4,
                    }
                ],
            }

            # Debug output
            print("Section Data:", section_data)
            print("Monthly Data:", monthly_data)
            print(f"Date Range: {start_date} to {end_date}")

            # Handle JSON request
            if request.headers.get("accept") == "application/json":
                return jsonify(
                    {
                        "stats": filtered_stats,
                        "section_data": section_data,
                        "monthly_data": monthly_data,
                    }
                )

            return render_template(
                "shipment_statistics.html",
                stats=filtered_stats,
                section_data=section_data,
                monthly_data=monthly_data,
            )

        return "Failed to retrieve statistics", 400

    except Exception as e:
        app.logger.error(f"Error in shipment_statistics: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({"error": str(e)}), 500


def filter_stats_by_date(stats, start_date, end_date):
    """Filter statistics based on date range."""
    try:
        filtered_stats = {
            "sections": stats["sections"].copy(),
            "weekly_sent": stats["weekly_sent"],
            "monthly_delivered": stats["monthly_delivered"],
            "weekly_sent_change": stats["weekly_sent_change"],
            "monthly_delivered_change": stats["monthly_delivered_change"],
            "yearly": stats["yearly"].copy(),
            "monthly_deliveries": {},
        }

        # Filter monthly deliveries
        for month, value in stats["monthly_deliveries"].items():
            month_date = datetime.strptime(month, "%B %Y")
            if start_date <= month_date <= end_date:
                filtered_stats["monthly_deliveries"][month] = value

        return filtered_stats

    except Exception as e:
        app.logger.error(f"Error filtering stats: {str(e)}")
        return stats  # Return original stats if filtering fails


@app.route("/populate_print_labels/<task_gid>", methods=["GET", "POST"])
def populate_print_labels(task_gid):
    try:
        app.logger.info(f"Populating print labels for task GID: {task_gid}")
        task_info = get_asana_task_info(task_gid)

        if not task_info:
            app.logger.error("Failed to retrieve task information")
            is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
            if is_ajax:
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": "Failed to retrieve task information from Asana",
                        }
                    ),
                    400,
                )
            flash("Failed to retrieve task information from Asana", "error")
            return redirect(url_for("select_lot"))

        # Store contact person from Asana task into session
        session["contact_person"] = task_info.get("Contact person", "")
        app.logger.info(f"Contact person from Asana: {session['contact_person']}")

        # Store label-free flag in session first
        label_free = task_info.get("label-free shipment ?", "").strip().lower()
        session["asana_label_free"] = label_free
        app.logger.info(f"Label-free flag from Asana: {label_free}")

        # Get comments from Asana - check multiple possible fields
        comments = None
        comment_fields = ["Shipment comments", "Comments", "Additional Comments"]

        for field in comment_fields:
            comment = task_info.get(field, "").strip()
            if comment and comment.lower() not in ["none", "n/a", ""]:
                comments = comment
                app.logger.info(f"Found comments in field '{field}': {comments}")
                break

        # Get other standard fields
        label_title = task_info.get("Ligentec label title", "")
        label_type = determine_label_type(task_info)
        lot_id = extract_lot_id(task_info)

        app.logger.info(f"Determined label type: {label_type}")
        app.logger.info(f"Extracted lot ID: {lot_id}")
        app.logger.info(f"Comments to be set: {comments}")

        # Get Erfurt-specific fields
        xfab_lot_id = task_info.get("XFAB Lot ID", "")
        xfab_po = task_info.get("XFAB purchase order", "")
        project_id = task_info.get("Project ID", "")

        # Parse wafer IDs and create pairs
        wafer_ids_raw = task_info.get("Wafers IDs", "")
        wafer_ids = re.findall(r"\d+[A-Z]+[A-Z0-9]+", wafer_ids_raw)
        slot_wafer_pairs = [
            (str(i + 1), wafer_id) for i, wafer_id in enumerate(wafer_ids)
        ]

        # Store everything in session
        session["asana_label_title"] = label_title
        session["asana_po"] = xfab_po if label_type == "2" else "N/A"
        session["asana_xfab_lot_id"] = xfab_lot_id if label_type == "2" else "N/A"
        session["asana_project_id"] = project_id if label_type == "2" else "N/A"
        session["asana_comments"] = comments if comments else ""
        session["paired_slots_wafers"] = slot_wafer_pairs
        session["asana_wafer_count"] = len(wafer_ids)
        session["asana_label_type"] = label_type
        if lot_id:
            session["current_lot"] = lot_id

        # Log final session state for debugging
        app.logger.info("Session data after population:")
        app.logger.info(f"Label Type: {session.get('asana_label_type')}")
        app.logger.info(f"Label Free: {session.get('asana_label_free')}")
        app.logger.info(f"Comments: {session.get('asana_comments')}")
        app.logger.info(f"Label Title: {session.get('asana_label_title')}")
        app.logger.info(f"Paired slots wafers: {session.get('paired_slots_wafers')}")
        app.logger.info(f"Contact Person: {session.get('contact_person')}")

        # Check if AJAX request
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        if is_ajax:
            return jsonify(
                {
                    "success": True,
                    "message": "Print label data populated successfully",
                    "data": {
                        "label_type": label_type,
                        "label_free": label_free,
                        "comments": comments,
                        "wafer_count": len(wafer_ids),
                        "contact_person": session.get("contact_person", ""),
                    },
                }
            )

        # Redirect for regular form submissions
        return redirect(url_for("print_labels"))

    except Exception as e:
        app.logger.error(f"Error in populate_print_labels: {str(e)}")
        app.logger.error(traceback.format_exc())

        # Check if AJAX request
        is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
        if is_ajax:
            return jsonify({"success": False, "message": str(e)}), 500

        flash(f"Error: {str(e)}", "error")
        return redirect(url_for("select_lot"))


def determine_label_type(task_info):
    """Determine the label type based on Asana task fields:
    - '2' for Erfurt
    - '1' for Label-free (no logo)
    - '3' for Standard (default)
    """
    try:
        app.logger.info("Determining label type from task info")

        # Check for Erfurt criteria
        xfab_lot_id = task_info.get("XFAB Lot ID", "").strip()
        xfab_po = task_info.get("XFAB purchase order", "").strip()
        erfurt_device_id = task_info.get("XFAB Device ID", "").strip()

        # Check for Label-free criteria
        label_free = task_info.get("label-free shipment ?", "").strip().lower()

        # Log the values for debugging
        app.logger.info(f"XFAB Lot ID: {xfab_lot_id}")
        app.logger.info(f"XFAB PO: {xfab_po}")
        app.logger.info(f"XFAB Device ID: {erfurt_device_id}")
        app.logger.info(f"Label-free shipment?: {label_free}")

        # Function to check if a field has valid content
        def is_valid_field(field):
            return field and field.lower() not in ["none", "n/a", ""]

        # Check label-free first, overriding Erfurt if present
        if label_free == "yes":
            app.logger.info("Detected Label-free type (overriding Erfurt if present)")
            return "1"  # Label-free priority
        # Then check Erfurt criteria
        elif any(
            is_valid_field(field) for field in [xfab_lot_id, xfab_po, erfurt_device_id]
        ):
            app.logger.info("Detected Erfurt label type")
            return "2"  # Erfurt
        else:
            app.logger.info("Using default Standard label type")
            return "3"  # Standard (default)

    except Exception as e:
        app.logger.error(f"Error determining label type: {str(e)}")
        app.logger.error(traceback.format_exc())
        return "3"  # Default to Standard in case of error


def extract_lot_id(task_info):
    """Extract the correct lot ID from Asana task info.
    Prioritizes LGT Lot ID over other fields.
    """
    try:
        # First try to get LGT Lot ID
        lgt_lot_id = task_info.get("LGT Lot ID", "").strip()

        # Log for debugging
        app.logger.info(f"Found LGT Lot ID: {lgt_lot_id}")

        # Check if it's a valid lot ID (not None or N/A)
        if lgt_lot_id and lgt_lot_id.lower() not in ["none", "n/a"]:
            return lgt_lot_id

        # If no valid LGT Lot ID, try to extract from task name
        task_name = task_info.get("Task Name", "")
        if task_name:
            # Look for patterns like "Shipment : XXXXX" or similar
            lot_match = re.search(r"Shipment\s*:\s*([^,\s]+)", task_name)
            if lot_match:
                return lot_match.group(1)

        return None

    except Exception as e:
        app.logger.error(f"Error extracting lot ID: {str(e)}")
        app.logger.error(traceback.format_exc())
        return None


def parse_wafer_ids(wafer_ids_raw: str) -> List[Tuple[str, str]]:
    wafer_entries = [
        entry.strip() for entry in wafer_ids_raw.split(";") if entry.strip()
    ]
    wafer_pairs = []
    for entry in wafer_entries:
        parts = entry.split(")")
        if len(parts) == 2:
            slot_part, wafer_id = parts
            slot = slot_part.split(",")[-1].strip()
            wafer_pairs.append((slot, wafer_id.strip()))
    return wafer_pairs


@app.route("/update_inventory", methods=["POST"])
def update_inventory():
    try:
        # Parse request data
        data = request.json
        wafer_ids = data.get("waferIds", [])
        location = data.get("location", "Sent to customer")
        asana_link = data.get("asanaLink", "")
        custom_location = data.get("customLocation", None)

        # Validate input
        if not wafer_ids or not location or not asana_link:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": (
                            "Missing required fields: waferIds, location, or asanaLink."
                        ),
                    }
                ),
                400,
            )

        # Use database operations to update inventory
        task_gid = extract_task_gid(asana_link)
        tracking_number = None

        if task_gid:
            task_info = get_asana_task_info(task_gid)
            if task_info:
                tracking_number = task_info.get("Tracking number")

        result = update_wafer_locations(
            wafer_ids,
            asana_link=asana_link,
            tracking_number=tracking_number,
            custom_location=custom_location,
        )

        return jsonify(result)

    except Exception as e:
        app.logger.error(f"Error in update_inventory endpoint: {str(e)}")
        return (
            jsonify(
                {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
            ),
            500,
        )


@app.route("/update_wafer_inventory", methods=["POST"])
def update_wafer_inventory():
    try:
        data = request.json
        app.logger.info(f"Received update request: {data}")

        # Extract all the fields from request
        asana_link = data.get("asanaLink")
        wafer_ids_raw = data.get("waferIds", "").strip()
        app.logger.info(f"Raw wafer IDs: {wafer_ids_raw}")

        if not all([asana_link, wafer_ids_raw]):
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Missing required fields: Asana link or wafer IDs",
                    }
                ),
                400,
            )

        # First get the Asana task info to get the correct lot ID
        task_gid = extract_task_gid(asana_link)
        app.logger.info(f"Extracted task GID: {task_gid}")

        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana task link"}),
                400,
            )

        task_info = get_asana_task_info(task_gid)
        app.logger.info(f"Task info from Asana: {task_info}")

        if not task_info:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Could not retrieve Asana task information",
                    }
                ),
                400,
            )

        # Get the lot ID from Asana task
        lot_id = task_info.get("LGT Lot ID")
        app.logger.info(f"Lot ID from Asana: {lot_id}")

        if not lot_id:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Could not find LGT Lot ID in Asana task",
                    }
                ),
                400,
            )

        # Parse wafer IDs and convert to uppercase
        wafer_ids = [
            wid.strip().upper()
            for wid in re.findall(r"\d+[A-Za-z]+[A-Za-z0-9]+", wafer_ids_raw)
        ]
        app.logger.info(f"Parsed and uppercase wafer IDs: {wafer_ids}")

        if not wafer_ids:
            return (
                jsonify({"success": False, "message": "No valid wafer IDs found"}),
                400,
            )

        # Extract tracking number from task info
        tracking_number = task_info.get("Tracking number")

        # Update wafer locations using database function
        result = update_wafer_locations(
            wafer_ids,
            asana_link=asana_link,
            tracking_number=tracking_number,
            lot_id=lot_id,
        )

        if not result["success"]:
            return jsonify({"success": False, "message": result["message"]}), 400

        return jsonify(
            {
                "success": True,
                "message": "Inventory updated successfully",
                "updated_data": {
                    "lot_id": lot_id,
                    "total_updated": result.get("updated_count", len(wafer_ids)),
                    "updated_wafers": wafer_ids,
                    "sent_date": result.get(
                        "sent_date", datetime.now().strftime("%Y-%m-%d")
                    ),
                    "received_date": result.get("received_date", ""),
                },
            }
        )

    except Exception as e:
        app.logger.error(f"Error in update_wafer_inventory: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
            ),
            500,
        )


@app.route("/asana_task_info/<task_gid>")
def asana_task_info(task_gid):
    """Show extracted information from Asana task for review"""
    try:
        # Get task info from Asana
        task_info = get_asana_task_info(task_gid)

        if not task_info:
            flash("Failed to retrieve task information from Asana", "error")
            return redirect(url_for("select_lot"))

        # Store in session for later use
        session["current_task_gid"] = task_gid

        return render_template(
            "asana_task_info.html", task_info=task_info, csrf_token=generate_csrf()
        )

    except Exception as e:
        app.logger.error(f"Error displaying Asana task info: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash(f"Error retrieving Asana task information: {str(e)}", "error")
        return redirect(url_for("select_lot"))


@app.route("/update_manual_inventory", methods=["POST"])
@csrf.exempt
def update_manual_inventory():
    try:
        data = request.json
        wafer_ids = data.get("waferIds", [])

        if not wafer_ids:
            return jsonify({"success": False, "message": "No wafer IDs provided"}), 400

        # Update wafer locations in database
        result = update_wafer_locations(wafer_ids)

        if not result["success"]:
            return jsonify({"success": False, "message": result["message"]}), 400

        return jsonify(
            {
                "success": True,
                "message": f"Updated {result['updated_count']} wafers",
                "dates": {
                    "sent_date": result["sent_date"],
                    "received_date": result["received_date"],
                },
            }
        )

    except Exception as e:
        app.logger.error(f"Error updating manual inventory: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/export_statistics/<format>")
def export_statistics(format):
    """Handle statistics export in PDF or Excel format"""
    try:
        project_gid = "1206397258493005"
        stats = get_shipment_stats_from_asana(project_gid)

        if not stats:
            return jsonify({"error": "No data available to export"}), 400

        if format == "excel":
            output = io.BytesIO()
            workbook = Workbook()
            ws = workbook.active
            ws.title = "Shipment Statistics"

            # Add headers
            headers = ["Section", "Count", "Monthly Trend"]
            ws.append(headers)

            # Add stats data
            for section, count in stats["sections"].items():
                ws.append(
                    [
                        section,
                        count,
                        stats.get("monthly_deliveries", {}).get(section, 0),
                    ]
                )

            # Add monthly trends
            ws.append([])
            ws.append(["Month", "Deliveries"])
            for month, count in stats["monthly_deliveries"].items():
                ws.append([month, count])

            workbook.save(output)
            output.seek(0)

            return send_file(
                output,
                mimetype=(
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                ),
                as_attachment=True,
                download_name=(
                    f"shipment_statistics_{datetime.now().strftime('%Y%m%d')}.xlsx"
                ),
            )

        elif format == "pdf":
            buffer = io.BytesIO()
            p = canvas.Canvas(buffer, pagesize=A4)
            width, height = A4

            # Add title and date
            p.setFont("Helvetica-Bold", 16)
            p.drawString(50, height - 50, "Shipment Statistics Report")
            p.setFont("Helvetica", 12)
            p.drawString(
                50,
                height - 70,
                f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            )

            # Add statistics
            y_position = height - 100
            for section, count in stats["sections"].items():
                p.drawString(50, y_position, f"{section}: {count}")
                y_position -= 20

            # Add monthly trends
            y_position -= 20
            p.drawString(50, y_position, "Monthly Deliveries")
            for month, count in stats["monthly_deliveries"].items():
                y_position -= 20
                p.drawString(50, y_position, f"{month}: {count}")

            p.save()
            buffer.seek(0)

            return send_file(
                buffer,
                mimetype="application/pdf",
                as_attachment=True,
                download_name=(
                    f"shipment_statistics_{datetime.now().strftime('%Y%m%d')}.pdf"
                ),
            )

    except Exception as e:
        app.logger.error(f"Export error: {str(e)}")
        return jsonify({"error": str(e)}), 500


@app.route("/share_statistics", methods=["POST"])
def share_statistics():
    try:
        data = request.get_json()
        email = data.get("email")
        include_charts = data.get("includeCharts", True)
        include_summary = data.get("includeSummary", True)

        if not email:
            return jsonify({"error": "Email address required"}), 400

        # Get statistics data
        project_gid = "1206397258493005"
        stats = get_shipment_stats_from_asana(project_gid)

        if not stats:
            return jsonify({"error": "No statistics data available"}), 400

        # Create PDF with selected components
        buffer = io.BytesIO()
        p = canvas.Canvas(buffer, pagesize=A4)
        width, height = A4

        # Add header
        p.setFont("Helvetica-Bold", 16)
        p.drawString(50, height - 50, "Shipment Statistics Report")
        p.setFont("Helvetica", 12)
        p.drawString(
            50,
            height - 70,
            f"Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M')}",
        )

        y_position = height - 100

        if include_summary:
            # Add summary statistics
            p.setFont("Helvetica-Bold", 14)
            p.drawString(50, y_position, "Summary Statistics")
            y_position -= 20

            p.setFont("Helvetica", 12)
            weekly_sent = stats["weekly_sent"]
            monthly_delivered = stats["monthly_delivered"]

            p.drawString(50, y_position, f"Weekly Sent: {weekly_sent}")
            y_position -= 20
            p.drawString(50, y_position, f"Monthly Delivered: {monthly_delivered}")
            y_position -= 40

        if include_charts and stats.get("section_data") and stats.get("monthly_data"):
            # Add charts if requested
            p.setFont("Helvetica-Bold", 14)
            p.drawString(50, y_position, "Charts")
            y_position -= 20

            # Convert charts to images and add to PDF
            for chart_id in ["sectionChart", "monthlyChart"]:
                chart_url = create_chart_image(stats, chart_id)
                if chart_url:
                    img = ImageReader(chart_url)
                    p.drawImage(img, 50, y_position - 200, width=400, height=200)
                    y_position -= 220

        p.save()
        buffer.seek(0)

        # Send email with PDF attachment
        msg = Message(
            "Shipment Statistics Report",
            sender=app.config["MAIL_DEFAULT_SENDER"],
            recipients=[email],
        )
        msg.body = "Please find attached the shipment statistics report."
        msg.attach(
            f"shipment_statistics_{datetime.now().strftime('%Y%m%d')}.pdf",
            "application/pdf",
            buffer.getvalue(),
        )
        mail.send(msg)

        return jsonify({"message": "Report shared successfully"})

    except Exception as e:
        app.logger.error(f"Error sharing statistics: {str(e)}")
        return jsonify({"error": str(e)}), 500


def create_chart_image(stats, chart_id):
    """Generates a chart image based on the provided stats and chart ID."""
    fig, ax = plt.subplots()
    if chart_id == "sectionChart":
        data = stats.get("section_data", {})
        ax.bar(data.keys(), data.values(), color="blue")
        ax.set_title("Section Statistics")
    elif chart_id == "monthlyChart":
        data = stats.get("monthly_data", {})
        ax.plot(data.keys(), data.values(), marker="o", linestyle="-", color="green")
        ax.set_title("Monthly Deliveries")

    buf = io.BytesIO()
    plt.savefig(buf, format="png")
    plt.close(fig)  # Free up memory by closing the figure
    buf.seek(0)
    return buf


@app.route("/quick_search", methods=["POST"])
@login_required
def quick_search():
    """Handle quick search functionality from the select_lot page.
    Searches for wafers based on lot number, scribe ID, and date range.
    """
    app.logger.info("Quick search endpoint called")
    try:
        # Extract and validate search parameters
        search_params, validation_error = extract_search_params(request.form)
        if validation_error:
            return validation_error

        # Perform the search
        results = search_wafers(search_params)
        app.logger.info(f"Search returned {len(results)} results")

        # Process search results
        return process_search_results(results)

    except Exception as e:
        return handle_search_error(e)


def extract_search_params(form_data):
    """Extract and validate search parameters from form data."""
    app.logger.info("Processing quick search request")
    app.logger.debug(f"Request form data: {form_data}")

    # Extract search parameters
    search_lot = form_data.get("search_lot", "").strip()
    search_scribe = form_data.get("search_scribe", "").strip()
    search_date_from = form_data.get("search_date_from", "").strip()
    search_date_to = form_data.get("search_date_to", "").strip()

    # Check if at least one search parameter is provided
    if not any([search_lot, search_scribe, search_date_from, search_date_to]):
        app.logger.warning("No search parameters provided")
        return None, (
            jsonify(
                {
                    "success": False,
                    "message": "Please enter at least one search parameter",
                }
            ),
            400,
        )

    # Build search parameters dictionary
    search_params = {}
    if search_lot:
        search_params["lot"] = search_lot
    if search_scribe:
        search_params["scribe_id"] = search_scribe
    if search_date_from:
        search_params["arrival_date_from"] = search_date_from
    if search_date_to:
        search_params["arrival_date_to"] = search_date_to

    app.logger.info(f"Search parameters: {search_params}")
    return search_params, None


def process_search_results(results):
    """Process search results and prepare response."""
    # If no results found
    if not results:
        return (
            jsonify(
                {
                    "success": False,
                    "message": "No wafers found matching your search criteria",
                }
            ),
            404,
        )

    # Extract lot IDs from results
    lot_ids = list(
        set([wafer.get("lot_id") for wafer in results if wafer.get("lot_id")])
    )
    app.logger.info(f"Found lot IDs: {lot_ids}")

    # If no valid lot IDs found
    if not lot_ids:
        return (
            jsonify(
                {
                    "success": False,
                    "message": "No valid lot IDs found in search results",
                }
            ),
            404,
        )

    # Join lot IDs with commas for multiple lots
    lot_string = ",".join(lot_ids)
    session["current_lot"] = lot_string
    session["search_results"] = results

    # Check if it's an AJAX request
    is_ajax = request.headers.get("X-Requested-With") == "XMLHttpRequest"
    if is_ajax:
        return jsonify(
            {
                "success": True,
                "redirect": url_for("wafer_details", lot=lot_string),
                "message": f"Found {len(results)} wafers in {len(lot_ids)} lots",
            }
        )
    else:
        return redirect(url_for("wafer_details", lot=lot_string))


def handle_search_error(error):
    """Handle errors in the search process."""
    app.logger.error(f"Error in quick search: {str(error)}")
    app.logger.error(traceback.format_exc())

    # If AJAX request, return JSON error
    if request.headers.get("X-Requested-With") == "XMLHttpRequest":
        return (
            jsonify(
                {
                    "success": False,
                    "message": f"An error occurred during search: {str(error)}",
                }
            ),
            500,
        )

    # Otherwise, flash error and redirect back to select_lot
    flash(f"An error occurred during search: {str(error)}", "error")
    return redirect(url_for("select_lot"))


@app.route("/advanced_search", methods=["GET", "POST"])
def advanced_search():
    form = AdvancedSearchForm()
    try:
        if request.method == "POST":
            # Collect search parameters
            search_params = {
                "lot": form.lot.data,
                "scribe_id": form.scribe_id.data,
                "location": form.location.data,
                "arrival_date_from": form.arrival_date_from.data,
                "arrival_date_to": form.arrival_date_to.data,
                "sent_date_from": form.sent_date_from.data,
                "sent_date_to": form.sent_date_to.data,
                "received_date_from": form.received_date_from.data,
                "received_date_to": form.received_date_to.data,
            }

            # Query database using search parameters
            results = search_wafers(search_params)

            if request.headers.get("X-Requested-With") == "XMLHttpRequest":
                return jsonify(
                    {"success": True, "data": results, "count": len(results)}
                )

            return render_template(
                "advanced_search.html", form=form, results=results, count=len(results)
            )

        return render_template("advanced_search.html", form=form)

    except Exception as e:
        app.logger.error(f"Error in advanced search: {str(e)}")
        app.logger.error(traceback.format_exc())
        if request.headers.get("X-Requested-With") == "XMLHttpRequest":
            return jsonify({"success": False, "error": str(e)}), 500
        flash(f"An error occurred: {str(e)}", "error")
        return render_template("advanced_search.html", form=form)


@app.route("/export_search_results", methods=["POST"])
def export_search_results():
    try:
        data = request.json
        results = data.get("results", [])
        export_format = data.get("format", "excel")

        if not results:
            return jsonify({"error": "No data to export"}), 400

        if export_format == "excel":
            # Create Excel file
            output = io.BytesIO()
            workbook = Workbook()
            ws = workbook.active

            # Write headers
            headers = ["Lot", "Scribe ID", "Location", "Sent Date", "Status"]
            ws.append(headers)

            # Write data
            for row in results:
                ws.append([row.get(h, "") for h in headers])

            workbook.save(output)
            output.seek(0)

            return send_file(
                output,
                mimetype=(
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                ),
                as_attachment=True,
                download_name=(
                    f"search_results_{datetime.now().strftime('%Y%m%d')}.xlsx"
                ),
            )

        else:
            return jsonify({"error": "Unsupported export format"}), 400

    except Exception as e:
        app.logger.error(f"Error exporting search results: {str(e)}")
        return jsonify({"error": str(e)}), 500


def check_eiger_project_criteria(task_info: Dict) -> bool:
    """Check if task meets Eiger project criteria using exact field names from Asana.
    Returns True if all criteria are met.
    """
    try:
        # Check required fields with exact names from Asana
        task_info_lookup = {k.lower().strip(): v for k, v in task_info.items()}

        lot_project = task_info_lookup.get("lot project", "").lower()
        priority = task_info_lookup.get("priority", "").lower()
        needs_review = task_info_lookup.get("need reviewing?", "").lower()
        label_free = task_info_lookup.get("label-free shipment ?", "").lower()

        app.logger.info(
            f"Checking Eiger criteria - Lot project: {lot_project}, "
            f"Priority: {priority}, Need Reviewing?: {needs_review}, "
            f"label-free: {label_free}"
        )

        # Verify all required criteria
        meets_criteria = (
            "eiger" in lot_project
            and "high" in priority
            and "yes" in needs_review
            and "yes" in label_free
        )

        app.logger.info(f"Meets Eiger criteria: {meets_criteria}")
        return meets_criteria

    except Exception as e:
        app.logger.error(f"Error checking Eiger criteria: {str(e)}")
        return False


def format_date_with_suffix(date: datetime) -> str:
    """Format date with proper suffix (e.g., 1st, 2nd, 3rd, 4th)"""
    day = date.day
    if 10 <= day % 100 <= 20:
        suffix = "th"
    else:
        suffix = {1: "st", 2: "nd", 3: "rd"}.get(day % 10, "th")
    return f"{day}{suffix} of {date.strftime('%B')}"


@app.route("/api/get_task_info", methods=["GET"])
def get_task_info_endpoint():
    try:
        task_gid = request.args.get("task_gid")

        # Handle task_gid with colon (format like "1210206616318785:1")
        if task_gid and ":" in task_gid:
            task_gid = task_gid.split(":")[0]

        if not task_gid:
            return (
                jsonify({"success": False, "message": "Missing task_gid parameter"}),
                400,
            )

        task_info = get_asana_task_info(task_gid)
        if not task_info:
            return jsonify({"success": False, "message": "Task not found"}), 404

        # Make sure the task_info has a name field for CSV filename generation
        if "Task Name" in task_info and "name" not in task_info:
            task_info["name"] = task_info["Task Name"]
        elif "Ligentec label title" in task_info and "name" not in task_info:
            task_info["name"] = task_info["Ligentec label title"]

        return jsonify({"success": True, "task_info": task_info})
    except Exception as e:
        logging.error(f"Error getting task info: {e}")
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/api/send_notification", methods=["POST"])
def send_notification():
    try:
        data = request.get_json()
        task_gid = data.get("task_gid")

        if not task_gid:
            return jsonify({"success": False, "message": "Task ID is required"}), 400

        # Get task info from Asana
        task_info = get_asana_task_info(task_gid)
        if not task_info:
            return (
                jsonify(
                    {"success": False, "message": "Failed to retrieve task information"}
                ),
                400,
            )

        # Check if this is an Eiger shipment by looking at the "Lot project" field
        lot_project = task_info.get("Lot project", "").strip()
        current_app.logger.info(f"Lot project: {lot_project}")

        # Instead of blocking, return a warning flag if not an Eiger project
        is_eiger_project = lot_project and "eiger" in lot_project.lower()
        # Return warning in response if not Eiger project but don't block
        eiger_warning = None
        if not is_eiger_project:
            eiger_warning = (
                "This does not appear to be an Eiger shipment "
                "(Lot project field does not contain 'Eiger')"
            )

        # Extract tracking number using exact field name from Asana
        tracking_number = task_info.get("Tracking number", "").strip()
        current_app.logger.info(f"Retrieved tracking number: {tracking_number}")

        if not tracking_number or tracking_number.lower() in ["n/a", "", "none"]:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Tracking Number is required. Please add it",
                    }
                ),
                400,
            )

        # Basic validation (must be at least 8 characters)
        if len(tracking_number) < 8:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Invalid tracking number format. Please check.",
                    }
                ),
                400,
            )

        # Get wafer IDs from session
        wafer_ids = [pair[1] for pair in session.get("paired_slots_wafers", [])]

        # Get manager acronym
        manager_full_name = task_info.get("Account/Project manager", "")
        manager_acronym = None
        if manager_full_name:
            name_parts = manager_full_name.split()
            if len(name_parts) >= 2:
                manager_acronym = (name_parts[0][:2] + name_parts[-1][0]).lower()

        # Send notification
        send_shipping_notification(
            tracking_number=tracking_number,
            ligentec_label_title=task_info.get("Ligentec label title", ""),
            wafer_ids=wafer_ids,
            manager_acronym=manager_acronym,
            contact_email=task_info.get("Contact person"),
        )

        response_data = {
            "success": True,
            "message": "Notification sent successfully",
        }

        # Add warning to response if present
        if eiger_warning:
            response_data["warning"] = eiger_warning
            response_data["is_eiger"] = False
        else:
            response_data["is_eiger"] = True

        return jsonify(response_data)

    except Exception as e:
        current_app.logger.error(f"Error sending notification: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/api/test_email", methods=["POST"])
def test_email():
    """Consolidated endpoint for testing email configuration with Eiger verification"""
    try:
        csrf_token = request.headers.get("X-CSRFToken")
        if not csrf_token:
            return jsonify({"success": False, "message": "CSRF token missing"}), 400

        # If task_gid is provided, verify it's an Eiger shipment
        data = request.get_json() or {}
        task_gid = data.get("task_gid")

        if task_gid:
            # Get task info from Asana
            task_info = get_asana_task_info(task_gid)
            if not task_info:
                return (
                    jsonify(
                        {
                            "success": False,
                            "message": "Failed to retrieve task information",
                        }
                    ),
                    400,
                )

            # Check if this is an Eiger shipment by looking at the "Lot project" field
            lot_project = task_info.get("Lot project", "").strip()
            current_app.logger.info(f"Test email - Lot project: {lot_project}")

            # Instead of blocking, return a warning flag if not an Eiger project
            is_eiger_project = lot_project and "eiger" in lot_project.lower()

            # Set warning if not Eiger project but don't block
            eiger_warning = None
            if not is_eiger_project:
                eiger_warning = (
                    "This does not appear to be an Eiger shipment "
                    "(Lot project field does not contain 'Eiger')"
                )

            current_app.logger.info(f"Eiger verification passed for task {task_gid}")

        # Use the dedicated test email function
        send_test_email()

        response_data = {"success": True, "message": "Test email sent successfully"}

        # Add warning to response if present
        if task_gid and "eiger_warning" in locals() and eiger_warning:
            response_data["warning"] = eiger_warning
            response_data["is_eiger"] = False
        elif task_gid:
            response_data["is_eiger"] = True

        return jsonify(response_data)

    except Exception as e:
        app.logger.error(f"Test email failed: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({"success": False, "message": str(e)}), 500


def get_field_value(task_info_lookup, possible_names):
    """Find field value using multiple possible field names."""
    for name in possible_names:
        if name.lower() in task_info_lookup:
            return task_info_lookup[name.lower()]
    return None


def validate_mandatory_fields(task_info_lookup, field_mappings):
    """Validate and retrieve mandatory fields."""
    mandatory_fields = {
        canonical_name: get_field_value(task_info_lookup, possible_names)
        for canonical_name, possible_names in field_mappings.items()
    }

    # Log the values found for debugging
    app.logger.info("Found field values:")
    for field, value in mandatory_fields.items():
        app.logger.info(f"{field}: {value}")

    # Check for missing fields
    missing_fields = [k for k, v in mandatory_fields.items() if not v]
    if missing_fields:
        error_msg = f"Missing mandatory fields: {', '.join(missing_fields)}"
        app.logger.error(error_msg)
        raise ValueError(error_msg)

    return mandatory_fields


def get_wafer_pairs(task_info, paired_slots_wafers):
    """Get wafer information either from session or task."""
    if paired_slots_wafers is None:
        wafer_ids_raw = task_info.get("Wafers IDs", "")
        paired_slots_wafers = [
            (str(i + 1), wid.strip())
            for i, wid in enumerate(re.findall(r"\d+[A-Z]+[A-Z0-9]+", wafer_ids_raw))
        ]

    if not paired_slots_wafers:
        raise ValueError("No wafer information available")

    return paired_slots_wafers


def determine_stack(lgt_lot_id):
    """Determine stack based on LGT lot ID pattern."""
    return "AN350" if lgt_lot_id.startswith("6A") else "AN800"


def create_row_data(mandatory_fields, task_info_lookup, slot, wafer_id, stack):
    """Create a single row of data for the CSV."""
    return {
        "Eiger Number": mandatory_fields["Eiger number"],
        "Tapeout": mandatory_fields["Tapeout(Eiger)"],
        "Wafer Size mm": 200,
        "Vendor Lot": mandatory_fields["Vendor lot"],
        "Customer Lot": mandatory_fields["Customer lot (Eiger)"],
        "Slot": slot,
        "Wafer": wafer_id,
        "Date received": "",
        "Tracking": task_info_lookup.get("tracking number", ""),
        "Receiver": (
            task_info_lookup.get("contact person")
            or task_info_lookup.get("email")
            or "<EMAIL>"
        ),
        "Stack": stack,
        "Rib": task_info_lookup.get("rib ?", ""),
        "TOX Target nm": task_info_lookup.get("tox target nm", ""),
        "Heaters": task_info_lookup.get("heaters?", ""),
        "Undercuts": task_info_lookup.get("undercuts?", ""),
        "SiN Tube Position (1-6)": task_info_lookup.get("sin tube position (1-6)", ""),
        "SiN Tube Wafer Slot": "",
        "Mask": task_info_lookup.get("mask", ""),
    }


def generate_eiger_csv(task_info, paired_slots_wafers=None):
    """Generate Eiger CSV from Asana task information and optional session data."""
    try:
        # Create case-insensitive lookup dictionary
        task_info_lookup = {k.lower().strip(): v for k, v in task_info.items()}

        # Define field mappings
        field_mappings = {
            "Eiger number": [
                "eiger number",
                "eiger_number",
                "eiger number ",
                "eiger_number",
                "eiger #",
            ],
            "Tapeout(Eiger)": ["tapeout (eiger)", "tapeout(eiger)", "tapeout eiger"],
            "Vendor lot": ["vendor lot", "vendor_lot", "vendor lot ", "vendor_lot"],
            "Customer lot (Eiger)": [
                "customer lot (eiger)",
                "customer_lot_eiger",
                "customer lot eiger",
            ],
        }

        # Validate and get mandatory fields
        mandatory_fields = validate_mandatory_fields(task_info_lookup, field_mappings)

        # Get wafer information
        paired_slots_wafers = get_wafer_pairs(task_info, paired_slots_wafers)

        # Determine stack
        stack = determine_stack(task_info_lookup.get("lgt lot id", ""))

        # Create rows
        rows = [
            create_row_data(mandatory_fields, task_info_lookup, slot, wafer_id, stack)
            for slot, wafer_id in paired_slots_wafers
        ]

        # Create DataFrame and save to CSV
        df = pd.DataFrame(rows)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"eiger_data_{timestamp}.csv"

        output = StringIO()
        df.to_csv(output, index=False)
        output.seek(0)

        return output, filename

    except Exception as e:
        app.logger.error(f"Error generating Eiger CSV: {str(e)}")
        raise


@app.route("/generate_eiger_csv", methods=["POST"])
def generate_eiger_csv_route():
    """Generate Eiger CSV from Asana task information."""
    try:
        # Get request data
        data = request.get_json()
        asana_link = data.get("asana_link")
        direct_generation = data.get("direct_generation", False)

        # Extract task GID and validate
        task_gid = extract_task_gid(asana_link)
        if not task_gid:
            return (
                jsonify({"success": False, "message": "Invalid Asana task link"}),
                400,
            )

        # Get task info and validate
        task_info = get_asana_task_info(task_gid)
        if not task_info:
            return (
                jsonify(
                    {"success": False, "message": "Failed to retrieve task information"}
                ),
                400,
            )

        # Get wafer information
        from routes.helpers.eiger_helpers import (
            create_eiger_filename,
            get_paired_slots_wafers,
            prepare_csv_response,
        )

        paired_slots_wafers = get_paired_slots_wafers(
            task_info, direct_generation, session.get("paired_slots_wafers")
        )

        # Generate CSV content
        string_output, _ = generate_eiger_csv(task_info, paired_slots_wafers)

        # Create the filename with Eiger prefix
        filename = create_eiger_filename(task_info)

        # Prepare and return the response
        return prepare_csv_response(string_output, filename)

    except ValueError as e:
        return jsonify({"success": False, "message": str(e)}), 400
    except Exception as e:
        app.logger.error(f"Error generating Eiger CSV: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {"success": False, "message": f"An unexpected error occurred: {str(e)}"}
            ),
            500,
        )


@app.route("/documentation")
def documentation():
    try:
        # Get system status for real-time information
        storage_status = {
            "database": check_db_connection(),
            "local_storage": check_local_storage_access(),
        }

        printer_status = check_printer_status()

        return render_template(
            "documentation.html",
            storage_status=storage_status,
            printer_status=printer_status,
        )
    except Exception as e:
        logging.error(f"Error loading documentation: {str(e)}")
        return render_template("documentation.html", error=str(e))


def check_printer_status():
    try:
        printer_status = subprocess.check_output(
            ["lpstat", "-t"], stderr=subprocess.STDOUT
        )
        return printer_status.decode("utf-8").strip()
    except Exception:
        return "Printer not available"


def check_db_connection():
    """Check database connection status"""
    try:
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
                cursor.fetchone()
            return True
    except Exception:
        return False


def check_local_storage_access():
    """Check local storage access"""
    try:
        # Check if download folder is accessible
        return os.path.exists(DOWNLOAD_FOLDER) and os.access(DOWNLOAD_FOLDER, os.W_OK)
    except Exception:
        return False


@app.route("/api/online-status")
def online_status():
    return {"online": True, "last_check": datetime.now().strftime("%Y-%m-%d %H:%M:%S")}


@app.route("/api/system-status")
def system_status():
    try:
        status = {
            "storage": {
                "database": check_db_connection(),
                "local_storage": check_local_storage_access(),
            },
            "printer": check_printer_status(),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        }
        return jsonify(status)
    except Exception as e:
        return jsonify({"error": str(e)}), 500


@app.route("/settings")
def settings():
    """Render settings page"""
    return render_template("settings.html")


@app.route("/api/settings", methods=["GET", "POST"])
def handle_settings():
    """API endpoint for handling settings"""
    try:
        if request.method == "GET":
            settings = load_settings()
            return jsonify(settings)

        elif request.method == "POST":
            new_settings = request.json

            # Validate settings
            if not validate_settings(new_settings):
                return (
                    jsonify({"success": False, "message": "Invalid settings provided"}),
                    400,
                )

            # Save settings
            if save_settings(new_settings):
                # Apply settings
                apply_settings(new_settings, app)
                return jsonify(
                    {"success": True, "message": "Settings saved successfully"}
                )
            else:
                return (
                    jsonify({"success": False, "message": "Failed to save settings"}),
                    500,
                )

    except Exception as e:
        app.logger.error(f"Settings error: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 500


@app.route("/api/health")
def health_check():
    try:
        # Get real system metrics
        from utils.health_check import get_health_metrics

        metrics = get_health_metrics()

        return jsonify({"success": True, "data": metrics})
    except Exception as e:
        app.logger.error(f"Health check error: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


def check_db_response_time():
    try:
        start_time = datetime.now()
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT 1")
        end_time = datetime.now()
        # Convert to milliseconds
        return (end_time - start_time).microseconds / 1000
    except Exception:
        return -1


def check_asana_connection():
    try:
        configuration = asana.Configuration()
        configuration.access_token = os.getenv("ASANA_ACCESS_TOKEN")
        api_client = asana.ApiClient(configuration)
        tasks_api_instance = asana.TasksApi(api_client)
        tasks_api_instance.get_tasks(opt_fields=["name"])
        return True
    except Exception:
        return False


def get_active_connections():
    try:
        # Count active database connections
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT count(*) FROM pg_stat_activity")
                return cursor.fetchone()[0]
    except Exception:
        return 0


def get_last_sync_time():
    try:
        # Get last database update time
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                cursor.execute("SELECT max(updated_at) FROM wafer_inventory")
                last_update = cursor.fetchone()[0]

                if last_update:
                    diff = datetime.now().timestamp() - last_update.timestamp()
                    if diff < 60:
                        return f"{int(diff)} seconds ago"
                    elif diff < 3600:
                        return f"{int(diff / 60)} minutes ago"
                    else:
                        return f"{int(diff / 3600)} hours ago"
        return "Unknown"
    except Exception:
        return "Unknown"


def get_labels_printed_today():
    try:
        # Ensure the download folder exists
        os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)

        # Count files in download folder from today
        today = datetime.now().strftime("%Y%m%d")
        count = sum(
            1
            for f in os.listdir(DOWNLOAD_FOLDER)
            if f.endswith("_label.pdf") and today in f
        )
        return count
    except Exception as e:
        app.logger.error(f"Error counting printed labels: {str(e)}")
        return 0


@socketio.on("connect")
def handle_connect():
    app.logger.info("Client connected")


def adjust_date_for_holidays_and_weekends(date):
    """Adjusts a date to skip weekends and public holidays.

    Args:
        date (datetime): The initial date to adjust.

    Returns:
        datetime: The adjusted date.
    """
    # Define public holidays (example for France)
    public_holidays = [
        datetime(date.year, 1, 1),  # New Year's Day
        datetime(date.year, 5, 1),  # Labor Day
        datetime(date.year, 7, 14),  # Bastille Day
        datetime(date.year, 12, 25),  # Christmas Day
        # Add more holidays as needed
    ]

    adjusted_date = date
    # Adjust date to skip weekends and public holidays
    while (
        adjusted_date.weekday() >= 5  # 5 and 6 are Saturday and Sunday
        or adjusted_date in public_holidays
    ):
        adjusted_date += timedelta(days=1)

    return adjusted_date


def get_system_uptime():
    """Get system uptime and performance metrics"""
    try:
        # Get system boot time
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        # Get system load
        cpu_load = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage("/")

        return {
            "uptime_days": uptime.days,
            "uptime_hours": uptime.seconds // 3600,
            "cpu_load": cpu_load,
            "memory_used": memory.percent,
            "disk_used": round((disk.used / disk.total) * 100, 2),
            "boot_time": boot_time.strftime("%Y-%m-%d %H:%M:%S"),
        }
    except Exception as e:
        app.logger.error(f"Error getting system uptime: {str(e)}")
        return {
            "uptime_days": 0,
            "uptime_hours": 0,
            "cpu_load": 0,
            "memory_used": 0,
            "disk_used": 0,
            "boot_time": "unknown",
        }


def get_recent_events(limit=10):
    """Get recent system events and activities"""
    try:
        events = []

        # Ensure the download folder exists
        os.makedirs(DOWNLOAD_FOLDER, exist_ok=True)

        # Check recent label generations
        label_files = sorted(
            [f for f in os.listdir(DOWNLOAD_FOLDER) if f.endswith("_label.pdf")],
            key=lambda x: os.path.getctime(os.path.join(DOWNLOAD_FOLDER, x)),
            reverse=True,
        )[:limit]

        for file in label_files:
            create_time = datetime.fromtimestamp(
                os.path.getctime(os.path.join(DOWNLOAD_FOLDER, file))
            )
            events.append(
                {
                    "type": "label_generation",
                    "description": f"Label generated: {file}",
                    "timestamp": create_time.strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "success",
                }
            )

        # Get recent inventory updates from DB
        try:
            with get_db_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(
                        """
                        SELECT w.wafer_id, wi.sent_at
                        FROM wafer_inventory wi
                        JOIN wafers w ON wi.wafer_id = w.wafer_id
                        WHERE wi.sent_at IS NOT NULL
                        ORDER BY wi.sent_at DESC
                        LIMIT %s
                        """,
                        (limit,),
                    )

                    recent_updates = cursor.fetchall()

                    for row in recent_updates:
                        events.append(
                            {
                                "type": "inventory_update",
                                "description": f"Wafer {row[0]} shipped",
                                "timestamp": (
                                    row[1].strftime("%Y-%m-%d %H:%M:%S")
                                    if row[1]
                                    else "Unknown"
                                ),
                                "status": "success",
                            }
                        )
        except Exception as db_error:
            app.logger.error(f"Database error getting recent events: {str(db_error)}")
            events.append(
                {
                    "type": "database",
                    "description": "Error fetching recent inventory updates",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "status": "error",
                }
            )

        # Sort all events by timestamp
        events.sort(key=lambda x: x["timestamp"], reverse=True)
        return events[:limit]

    except Exception as e:
        app.logger.error(f"Error getting recent events: {str(e)}")
        return [
            {
                "type": "error",
                "description": "Error retrieving recent events",
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "error",
            }
        ]


@app.route("/api/system-insights")
def get_system_insights():
    try:
        # Get database insights
        with get_db_connection() as conn:
            with conn.cursor() as cursor:
                # Get total shipments
                cursor.execute(
                    "SELECT COUNT(*) FROM wafer_inventory WHERE sent_at IS NOT NULL"
                )
                total_shipments = cursor.fetchone()[0]

                # Get active lots
                cursor.execute("SELECT COUNT(DISTINCT lot_id) FROM wafers")
                active_lots = cursor.fetchone()[0]

        return jsonify(
            {
                "success": True,
                "data": {
                    "totalShipments": total_shipments,
                    "activeLots": active_lots,
                    "systemUptime": get_system_uptime(),
                    "recentEvents": get_recent_events(),
                },
            }
        )
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/api/metric-details/<metric_type>")
def get_metric_details(metric_type):
    try:
        if metric_type == "uptime":
            return get_uptime_details()
        else:
            return jsonify({"error": "Invalid metric type"}), 400
    except Exception as e:
        return jsonify({"error": str(e)}), 500


def emit_system_event(event_type, message):
    socketio.emit(
        "system_event",
        {
            "type": event_type,
            "message": message,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        },
    )


def get_uptime_details():
    """Get detailed system uptime and performance metrics"""
    try:
        # Get basic uptime info
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = datetime.now() - boot_time

        # Get detailed CPU info
        cpu_times = psutil.cpu_times_percent()
        cpu_freq = psutil.cpu_freq()
        cpu_stats = psutil.cpu_stats()

        # Get detailed memory info
        memory = psutil.virtual_memory()
        swap = psutil.swap_memory()

        # Get disk IO statistics
        disk_io = psutil.disk_io_counters()
        disk_usage = psutil.disk_usage("/")

        # Get network statistics
        network = psutil.net_io_counters()

        # Calculate performance scores
        cpu_score = 100 - cpu_times.idle
        memory_score = memory.percent
        disk_score = (disk_usage.used / disk_usage.total) * 100

        # Get hourly load averages
        load_avg = {
            "last_hour": os.getloadavg()[0],
            "last_5_minutes": os.getloadavg()[1],
            "last_15_minutes": os.getloadavg()[2],
        }

        return jsonify(
            {
                "success": True,
                "data": {
                    "uptime": {
                        "days": uptime.days,
                        "hours": uptime.seconds // 3600,
                        "minutes": (uptime.seconds % 3600) // 60,
                        "total_seconds": uptime.total_seconds(),
                        "boot_time": boot_time.strftime("%Y-%m-%d %H:%M:%S"),
                    },
                    "cpu": {
                        "usage_percent": cpu_score,
                        "frequency_current": (
                            round(cpu_freq.current, 2) if cpu_freq else 0
                        ),
                        "frequency_max": (
                            round(cpu_freq.max, 2) if cpu_freq and cpu_freq.max else 0
                        ),
                        "cores": psutil.cpu_count(),
                        "threads": psutil.cpu_count(logical=True),
                        "interrupts": cpu_stats.interrupts,
                        "ctx_switches": cpu_stats.ctx_switches,
                    },
                    "memory": {
                        "total": memory.total,
                        "available": memory.available,
                        "used": memory.used,
                        "used_percent": memory.percent,
                        "swap_used": swap.used,
                        "swap_free": swap.free,
                    },
                    "disk": {
                        "total": disk_usage.total,
                        "used": disk_usage.used,
                        "free": disk_usage.free,
                        "used_percent": disk_score,
                        "read_count": disk_io.read_count if disk_io else 0,
                        "write_count": disk_io.write_count if disk_io else 0,
                    },
                    "network": {
                        "bytes_sent": network.bytes_sent,
                        "bytes_recv": network.bytes_recv,
                        "packets_sent": network.packets_sent,
                        "packets_recv": network.packets_recv,
                    },
                    "load_averages": load_avg,
                    "performance_scores": {
                        "cpu": round(cpu_score, 2),
                        "memory": round(memory_score, 2),
                        "disk": round(disk_score, 2),
                        "overall": round(
                            (cpu_score + memory_score + disk_score) / 3, 2
                        ),
                    },
                },
            }
        )

    except Exception as e:
        app.logger.error(f"Error getting uptime details: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500


@app.route("/faqs")
def faqs() -> str:
    """Handle FAQ page requests.

    Returns:
        str: Rendered FAQ template with categorized questions and answers.
    """
    return render_template("faqs.html", faqs=get_faq_data())


def get_faq_data() -> Dict[str, Any]:
    """Retrieve FAQ data organized by categories.

    Returns:
        Dict[str, Any]: Structured FAQ data with categories, questions and answers.
    """
    return {
        "general": {
            "title": "General Questions",
            "questions": get_general_faqs(),
        },
        "technical": {
            "title": "Technical Support",
            "questions": get_technical_faqs(),
        },
        "integration": {
            "title": "Integration & Connectivity",
            "questions": get_integration_faqs(),
        },
    }


def get_general_faqs() -> list:
    """Get general FAQ questions and answers."""
    return [
        {
            "question": "How do I generate a new label?",
            "answer": (
                'Navigate to "Print Labels", select your lot number or Asana link, '
                "choose your template type, fill in the required fields, and click "
                '"Generate Label".'
                "choose your template type, fill in the required fields, and click "
                '"Generate Label".'
            ),
        },
        {
            "question": "What are the different types of labels available?",
            "answer": (
                "We offer three types of labels: Standard Label (with logo), "
                "Erfurt Label (with additional fields for X-FAB), and "
                "Label-Free format."
            ),
        },
        {
            "question": "Can I customize label templates?",
            "answer": (
                "Yes, you can customize templates through the Label Templates section. "
                "Each template type has configurable fields and formatting options."
            ),
        },
    ]


def get_technical_faqs() -> list:
    """Get technical support FAQ questions and answers."""
    return [
        {
            "question": "What should I do if the printer is not responding?",
            "answer": (
                "First, check the printer connection and ensure it's powered on. "
                "Verify the IP address is correct in your settings. "
                "If issues persist, contact IT support."
            ),
        },
        {
            "question": "How do I update wafer location after shipping?",
            "answer": (
                'After generating labels and shipping, use the "Update Inventory" '
                "feature to mark wafers as shipped. This automatically updates their "
                "location in the system."
            ),
        },
        {
            "question": "What file formats are supported for label export?",
            "answer": (
                "Labels can be exported as PDF files. For packing slips, both PDF "
                "and Excel formats are supported."
            ),
        },
    ]


def get_integration_faqs() -> list:
    """Get integration FAQ questions and answers."""
    return [
        {
            "question": "How does the Asana integration work?",
            "answer": (
                "Paste an Asana task URL to automatically populate label information. "
                "The system extracts relevant fields and updates the task status "
                "after shipping."
            ),
        },
        {
            "question": "Can I access the inventory data remotely?",
            "answer": (
                "Yes, the system connects to the central database, allowing secure "
                "remote access to inventory data through your Ligentec credentials."
            ),
        },
        {
            "question": "How often is the inventory data synchronized?",
            "answer": (
                "Inventory data is synchronized in real-time for critical operations "
                "to ensure data consistency."
            ),
        },
    ]


@app.route("/support")
def support():
    try:
        # Generate CSRF token for the template
        csrf_token = generate_csrf()
        return render_template("support.html", csrf_token=csrf_token)
    except Exception as e:
        app.logger.error(f"Error rendering support page: {str(e)}")
        return render_template("error.html", error="Unable to load support page"), 500


@app.route("/inventory_management")
def inventory_management():
    return render_template("inventory_management.html")


@app.route("/location_management")
def location_management():
    return render_template("location_management.html")


@app.route("/api/location/add", methods=["POST"])
def add_location():
    try:
        data = request.get_json()
        with get_sqlalchemy_connection() as conn:
            query = text(
                """
                INSERT INTO locations (
                    location_id, label, address, email, telephone,
                    updated_at, updated_by
                )
                VALUES (
                    :location_id, :label, :address, :email, :telephone,
                    NOW(), 'system'
                )
                RETURNING location_id
            """
            )
            result = conn.execute(
                query,
                {
                    "location_id": data["locationId"],
                    "label": data["label"],
                    "address": data["address"],
                    "email": data["email"],
                    "telephone": data["telephone"],
                },
            )
            new_id = result.scalar()
            return jsonify({"success": True, "id": new_id})
    except Exception as e:
        return jsonify({"success": False, "message": str(e)}), 500


def get_sqlalchemy_connection():
    return engine.connect()


@app.route("/label_templates")
def label_templates():
    try:
        # Define available templates with their metadata
        templates = {
            "labels": {
                "standard": {
                    "name": "Standard Label",
                    "description": "Default label format for general shipments",
                    "fields": ["title", "date", "wafer_count"],
                    "type": "label",
                },
                "erfurt": {
                    "name": "Erfurt Label",
                    "description": (
                        "Special format for Erfurt shipments with additional fields"
                    ),
                    "fields": [
                        "title",
                        "date",
                        "wafer_count",
                        "po",
                        "project_id",
                        "xfab_lot_id",
                    ],
                    "type": "label",
                },
                "label_free": {
                    "name": "Label-Free",
                    "description": "Minimal label format without logo",
                    "fields": ["title", "date", "wafer_count"],
                    "type": "label",
                },
            },
            "packing_slips": {
                "standard": {
                    "name": "Standard Packing Slip",
                    "description": "Default packing slip with basic shipping details",
                    "fields": ["title", "date", "wafer_count", "comments"],
                    "type": "packing_slip",
                },
                "erfurt": {
                    "name": "Erfurt Packing Slip",
                    "description": "Detailed packing slip for Erfurt shipments",
                    "fields": [
                        "title",
                        "date",
                        "wafer_count",
                        "po",
                        "project_id",
                        "xfab_lot_id",
                        "comments",
                    ],
                    "type": "packing_slip",
                },
                "detailed": {
                    "name": "Detailed Packing Slip",
                    "description": "Comprehensive packing slip with all details",
                    "fields": [
                        "title",
                        "date",
                        "wafer_count",
                        "tracking_number",
                        "special_instructions",
                        "comments",
                    ],
                    "type": "packing_slip",
                },
            },
        }

        app.logger.info("Rendering label_templates.html with templates")
        return render_template("label_templates.html", templates=templates)

    except Exception as e:
        app.logger.error(f"Error in label_templates route: {str(e)}")
        app.logger.error(traceback.format_exc())
        flash("Error loading templates", "error")
        return redirect(url_for("home"))


@app.route("/preferences")
def preferences():
    return render_template("preferences.html")


@app.route("/about")
def about():
    return render_template("about.html")


@app.route("/static/img/logoTalaria.jpeg")
def profile_image():
    return send_file("static/img/logoTalaria.jpeg")


@app.route("/profile/image/<filename>")
@login_required
def profile_image_file(filename):
    """Serve profile images"""
    return send_file(os.path.join("static", "uploads", "profile_images", filename))


@app.route("/api/templates/<type>/<template_id>/preview")
def preview_template(type, template_id):
    try:
        # Sample data for preview
        sample_data = {
            "title": "Sample Shipment",
            "date": datetime.now().strftime("%Y-%m-%d"),
            "wafer_count": 5,
            "contact_person": "",  # Optional field for receiver's name
            "po": "PO123456",
            "project_id": "PROJ789",
            "xfab_lot_id": "LOT456",
            "tracking_number": "TRK123456789",
            "comments": "Handle with care. Temperature sensitive materials.",
            "special_instructions": "Keep at room temperature",
            "wafer_ids": ["W001", "W002", "W003", "W004", "W005"],
        }

        if type == "label":
            preview_html = generate_label_preview(template_id, sample_data)
        else:
            preview_html = generate_packing_slip_preview(template_id, sample_data)

        return jsonify({"success": True, "html": preview_html})
    except Exception as e:
        return jsonify({"success": False, "error": str(e)}), 500


def generate_packing_slip_preview(template_id, data):
    """Generate HTML preview for packing slip templates"""
    logo_html = (
        '<div class="mb-4">'
        '<img src="/static/img/logo.gif" alt="Logo" class="h-12">'
        "</div>"
    )

    # Base content for all packing slip templates
    base_content = f"""
        <p><strong>Title:</strong> {data["title"]}</p>
        <p><strong>Date:</strong> {data["date"]}</p>
        <p><strong>Attn To:</strong> {data.get("contact_person", "")}</p>
        <p><strong>Wafer Count:</strong> {data["wafer_count"]}</p>
        <p><strong>Comments:</strong> {data["comments"]}</p>
    """

    # Generate wafer list
    wafer_list = (
        '<div class="mt-4"><strong>Wafers:</strong><ul class="list-disc pl-5 mt-2">'
    )
    for wafer_id in data["wafer_ids"]:
        wafer_list += f"<li>{wafer_id}</li>"
    wafer_list += "</ul></div>"

    if template_id == "erfurt":
        content = (
            base_content
            + f"""
            <p><strong>PO:</strong> {data["po"]}</p>
            <p><strong>Project ID:</strong> {data["project_id"]}</p>
            <p><strong>X-FAB Lot ID:</strong> {data["xfab_lot_id"]}</p>
            {wafer_list}
        """
        )
    elif template_id == "detailed":
        content = (
            base_content
            + f"""
            <p><strong>Tracking Number:</strong> {data["tracking_number"]}</p>
            <p><strong>Special Instructions:</strong> {data["special_instructions"]}</p>
            {wafer_list}
        """
        )
    else:  # standard template
        content = base_content + wafer_list

    return f"""
        <div class="p-4 border rounded">
            {logo_html}
            <div class="space-y-2">
                {content}
            </div>
        </div>
    """


def generate_label_preview(template_id, data):
    """Generate HTML preview for label templates"""
    logo_html = (
        '<div class="mb-4">'
        '<img src="/static/img/logo.gif" alt="Logo" class="h-12">'
        "</div>"
    )
    qr_code = generate_sample_qr()

    if template_id == "erfurt":
        content = f"""
            {logo_html}
            <div class="space-y-2">
                <p><strong>Title:</strong> {data["title"]}</p>
                <p><strong>Date:</strong> {data["date"]}</p>
                <p><strong>Wafer Count:</strong> {data["wafer_count"]}</p>
                <p><strong>PO:</strong> {data["po"]}</p>
                <p><strong>Project ID:</strong> {data["project_id"]}</p>
                <p><strong>X-FAB Lot ID:</strong> {data["xfab_lot_id"]}</p>
            </div>
        """
    elif template_id == "label_free":
        content = f"""
            <div class="space-y-2">
                <p><strong>Title:</strong> {data["title"]}</p>
                <p><strong>Date:</strong> {data["date"]}</p>
                <p><strong>Wafer Count:</strong> {data["wafer_count"]}</p>
            </div>
        """
    else:  # standard template
        content = f"""
            {logo_html}
            <div class="

space-y-2">
                <p><strong>Title:</strong> {data["title"]}</p>
                <p><strong>Date:</strong> {data["date"]}</p>
                <p><strong>Wafer Count:</strong> {data["wafer_count"]}</p>
            </div>
        """

    return f"""
        <div class="p-4 border rounded">
            {content}
            <div class="mt-4">
                <img src="data:image/png;base64,{qr_code}" alt="QR Code" class="h-32">
            </div>
        </div>
    """


def generate_sample_qr():
    """Generate a sample QR code base64 string"""
    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    qr.add_data("Sample QR Code")
    qr.make(fit=True)
    img = qr.make_image(fill_color="black", back_color="white")

    buffered = BytesIO()
    img.save(buffered, format="PNG")
    return base64.b64encode(buffered.getvalue()).decode()


# Helper function to sanitize filenames


@app.route("/chatbot")
def chatbot():
    return render_template("chatbot.html")


# Test printer connection function


@app.route("/test_printer_connection", methods=["POST"])
@csrf.exempt
def test_printer_connection():
    try:
        if not request.is_json:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Invalid request format - expecting JSON",
                    }
                ),
                400,
            )

        data = request.get_json()
        printer_ip = data.get("printer_ip")

        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)

        result = sock.connect_ex((printer_ip, 9100))
        sock.close()

        return jsonify(
            {
                "success": result == 0,
                "message": (
                    "Printer is accessible"
                    if result == 0
                    else f"Could not connect (Error: {result})"
                ),
            }
        )

    except Exception as e:
        app.logger.error(f"Test connection error: {str(e)}")
        return (
            jsonify({"success": False, "message": f"Connection test failed: {str(e)}"}),
            500,
        )


# Print to Honeywell function


@app.route("/print_to_honeywell", methods=["POST"])
@csrf.exempt
def print_to_honeywell():
    try:
        app.logger.info("Print endpoint called")
        if not request.is_json:
            return (
                jsonify(
                    {
                        "success": False,
                        "message": "Invalid request format - expecting JSON",
                    }
                ),
                400,
            )

        data = request.get_json()
        printer_ip = data.get("printer_ip")
        copy_number = int(data.get("copy_number", 1))

        app.logger.info(f"Printing to {printer_ip}, copies: {copy_number}")

        # Use the already generated label file from session
        label_file_path = session.get("label_file_path")
        if not label_file_path or not os.path.exists(label_file_path):
            raise FileNotFoundError(
                "Label file not found. Please generate the label first."
            )

        # Send to printer
        with open(label_file_path, "rb") as pdf_file:
            pdf_data = pdf_file.read()
            printer_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            printer_socket.settimeout(10)
            printer_socket.connect((printer_ip, 9100))

            for i in range(copy_number):
                printer_socket.sendall(pdf_data)
                app.logger.info(f"Sent copy {i + 1}")

            printer_socket.close()

        return jsonify({"success": True, "message": "Label printed successfully"})

    except FileNotFoundError as e:
        app.logger.error(f"File not found error: {str(e)}")
        return jsonify({"success": False, "message": str(e)}), 404
    except Exception as e:
        app.logger.error(f"Print error: {str(e)}")
        app.logger.error(traceback.format_exc())
        return jsonify({"success": False, "message": f"Print failed: {str(e)}"}), 500


# Generate label function


def generate_label(session_data):
    """Generate a label with support for long titles and proper text wrapping."""
    try:
        # Extract data and prepare file path
        label_data = extract_label_data(session_data)
        label_file_path = create_label_file_path(label_data["label_title"])

        # Create the image
        img, draw, font = initialize_label_image(label_data["label_type"])

        # Draw label content
        draw_label_content(img, draw, font, label_data)

        # Save to PDF and return the file path
        save_image_to_pdf(img, label_file_path)

        return label_file_path

    except Exception as e:
        app.logger.error(f"Error generating label: {str(e)}")
        app.logger.error(traceback.format_exc())
        raise


def extract_label_data(session_data):
    """Extract required data from session for label generation."""
    new_slot_ids = session_data.get("new_slot_ids", [])
    paired_slots_wafers = session_data.get("paired_slots_wafers", [])

    # Replace original slot IDs with new ones if available
    if new_slot_ids and len(new_slot_ids) == len(paired_slots_wafers):
        paired_slots_wafers = [
            (new_slot_ids[i], wafer_id)
            for i, (_, wafer_id) in enumerate(paired_slots_wafers)
        ]

    return {
        "paired_slots_wafers": paired_slots_wafers,
        "label_type": session_data.get("label_type"),
        "shipment_date": session_data.get("shipment_date"),
        "label_title": session_data.get("title", ""),
        "wafer_count": session_data.get("wafer_count"),
        "po": session_data.get("po"),
        "project_id": session_data.get("project_id"),
        "xfab_lot_id": session_data.get("xfab_lot_id"),
    }


def initialize_label_image(label_type):
    """Initialize the label image and drawing context."""
    # Set up image dimensions (9cm x 15cm converted to points at 72dpi)
    img_width, img_height = int(9 * 28.3465 * 3), int(15 * 28.3465 * 3)
    img = Image.new("RGB", (img_width, img_height), color="white")
    draw = ImageDraw.Draw(img)

    # Add logo if not label-free
    if label_type != "1":  # Not label-free
        logo_path = GENERAL_LOGO_PATH
        if os.path.exists(logo_path):
            logo = Image.open(logo_path)
            img.paste(logo, (50, 50))

    # Get font for the label
    font = get_label_font()

    # Log which font we're using for debugging
    app.logger.info(
        f"Using font for label: "
        f"{font.getname() if hasattr(font, 'getname') else 'Unknown'}"
    )

    return img, draw, font


def draw_label_content(img, draw, font, data):
    """Draw all content on the label image."""
    # Start position for text based on label type
    text_y = 200 if data["label_type"] != "1" else 100

    # Draw title with text wrapping
    text_y = draw_label_title(img, draw, font, data["label_title"], text_y)

    # Draw other standard fields
    text_y = draw_standard_fields(draw, font, data, text_y)

    # Draw wafer list
    text_y = draw_wafer_list(draw, font, data, text_y)

    # Add QR code
    add_qr_code(img, data)


def draw_label_title(img, draw, font, title, text_y):
    """Draw the title with text wrapping."""
    # Calculate max width for text
    img_width = img.width
    max_text_width = img_width - 140  # 70px margin on each side

    # Use environment-forced font if available
    font_name = os.environ.get("FORCE_FONT", "Helvetica")
    font_size = 12  # Approximate font size for PIL font

    # Wrap the title text with the new function
    title_lines = wrap_text(
        f"Label Title: {title}", font_name, font_size, max_text_width
    )

    # Draw each line
    for line in title_lines:
        draw.text((70, text_y), line, fill=(0, 0, 0), font=font)
        text_y += 40  # Spacing between lines

    return text_y + 20  # Extra space after title block


def draw_standard_fields(draw, font, data, text_y):
    """Draw standard label fields."""
    # Draw date
    draw.text((70, text_y), f"Date: {data['shipment_date']}", fill=(0, 0, 0), font=font)
    text_y += 60

    # Draw wafer count
    draw.text(
        (70, text_y), f"Wafers number: {data['wafer_count']}", fill=(0, 0, 0), font=font
    )
    text_y += 60

    # Add Erfurt-specific fields for label type 2
    if data["label_type"] == "2":
        text_y = draw_erfurt_fields(draw, font, data, text_y)

    return text_y


def draw_erfurt_fields(draw, font, data, text_y):
    """Draw Erfurt-specific fields for label type 2."""
    # Add purchase order if available
    if data["po"]:
        draw.text(
            (70, text_y), f"Purchase order: {data['po']}", fill=(0, 0, 0), font=font
        )
        text_y += 60

    # Add project ID if available
    if data["project_id"]:
        draw.text(
            (70, text_y), f"Project ID: {data['project_id']}", fill=(0, 0, 0), font=font
        )
        text_y += 60

    # Add XFAB lot ID if available
    if data["xfab_lot_id"]:
        draw.text(
            (70, text_y),
            f"XFAB Lot ID: {data['xfab_lot_id']}",
            fill=(0, 0, 0),
            font=font,
        )
        text_y += 60

    return text_y


def draw_wafer_list(draw, font, data, text_y):
    """Draw the wafer list on the label."""
    paired_slots_wafers = data["paired_slots_wafers"]

    draw.text((70, text_y), "Slot ID,Wafer ID", fill=(0, 0, 0), font=font)
    text_y += 60

    for i in range(0, len(paired_slots_wafers), 2):
        slot_id, wafer_id = paired_slots_wafers[i]
        line = f"{slot_id},{wafer_id}"

        if i + 1 < len(paired_slots_wafers):
            next_slot, next_wafer = paired_slots_wafers[i + 1]
            line += f"  {next_slot},{next_wafer}"

        draw.text((70, text_y), line, fill=(0, 0, 0), font=font)
        text_y += 40

    return text_y


def add_qr_code(img, data):
    """Generate and add QR code to the label image."""
    paired_slots_wafers = data["paired_slots_wafers"]
    wafer_list = [f"{slot},{wafer_id}" for slot, wafer_id in paired_slots_wafers]

    qr = qrcode.QRCode(version=1, box_size=10, border=4)
    qr.add_data("\n".join(wafer_list))
    qr.make(fit=True)
    qr_img = qr.make_image(fill_color="black", back_color="white")

    qr_size = int(11 * 28.3465)
    qr_img_resized = qr_img.resize((qr_size, qr_size))
    img.paste(qr_img_resized, (420, 950))


def save_image_to_pdf(img, file_path):
    """Save the image to PDF file."""
    # Convert image to PDF
    img_width, img_height = img.size
    c = canvas.Canvas(file_path, pagesize=(img_width / 3, img_height / 3))
    c.drawImage(ImageReader(img), 0, 0, width=img_width / 3, height=img_height / 3)
    c.save()


@app.route("/api/dashboard/stats")
@login_required
def dashboard_stats():
    """API endpoint to get dashboard statistics"""
    try:
        # Get inventory statistics from database
        inventory_stats = get_inventory_stats()

        # Get Asana project stats
        project_gid = "1206397258493005"
        dashboard_service = app.config.get("DASHBOARD_SERVICE")

        if dashboard_service and dashboard_service.client:
            asana_stats = dashboard_service.get_shipment_stats(project_gid)
            section_distribution = dashboard_service.get_section_distribution(
                project_gid
            )
            monthly_deliveries = dashboard_service.get_monthly_deliveries(project_gid)
            recent_shipments = dashboard_service.get_recent_shipments(
                project_gid, limit=5
            )
        else:
            # Fallback if service not available
            app.logger.warning("Dashboard service not available, using fallback data")
            asana_stats = {}
            section_distribution = {"labels": [], "datasets": [{"data": []}]}
            monthly_deliveries = {"labels": [], "datasets": [{"data": []}]}
            recent_shipments = {"shipments": [], "pagination": {"total": 0}}

        # Format last updated time
        last_updated = datetime.now().strftime("%b %d, %Y, %I:%M %p")

        return jsonify(
            {
                "available_lots": inventory_stats.get("available_lots", 0),
                "available_wafers": inventory_stats.get("available_wafers", 0),
                "shipped_wafers": asana_stats.get("shipped_wafers", 0),
                "asana_stats": asana_stats,
                "section_data": section_distribution,
                "monthly_data": monthly_deliveries,
                "recent_shipments": recent_shipments.get("shipments", []),
                "last_updated": last_updated,
            }
        )

    except Exception as e:
        app.logger.error(f"Error getting dashboard stats: {str(e)}")
        app.logger.error(traceback.format_exc())
        return (
            jsonify(
                {
                    "error": "Failed to retrieve dashboard statistics",
                    "available_lots": 0,
                    "available_wafers": 0,
                    "shipped_wafers": 0,
                    "asana_stats": {},
                    "section_data": {"labels": [], "datasets": [{"data": []}]},
                    "monthly_data": {"labels": [], "datasets": [{"data": []}]},
                    "recent_shipments": [],
                    "last_updated": "Data unavailable",
                }
            ),
            500,
        )


def get_section_distribution_from_asana(project_gid):
    """Get section distribution data from Asana for chart display"""
    try:
        # Initialize Asana API client
        access_token = os.environ.get(
            "ASANA_ACCESS_TOKEN", "***************************************************"
        )
        client = asana.Client.access_token(access_token)

        # Get sections from project
        sections = client.sections.find_by_project(project_gid)

        # Prepare data structure for chart
        section_data = {
            "labels": [],
            "datasets": [
                {
                    "data": [],
                    "backgroundColor": [
                        "#3B82F6",
                        "#10B981",
                        "#F59E0B",
                        "#EF4444",
                        "#8B5CF6",
                        "#EC4899",
                        "#60A5FA",
                    ],
                }
            ],
        }

        # Get tasks count for each section
        for section in sections:
            # Get tasks in this section
            tasks = client.tasks.find_by_section(
                section["gid"], {"opt_fields": "name,completed"}
            )

            # Count tasks
            task_count = sum(1 for _ in tasks)

            # Add to dataset
            section_data["labels"].append(section["name"])
            section_data["datasets"][0]["data"].append(task_count)

        return section_data

    except Exception as e:
        app.logger.error(f"Error getting section distribution: {str(e)}")
        return {"labels": [], "datasets": [{"data": []}]}


def get_monthly_deliveries_from_asana(project_gid):
    """Get monthly deliveries data from Asana for chart display"""
    try:
        # Initialize Asana API client
        access_token = os.environ.get(
            "ASANA_ACCESS_TOKEN", "***************************************************"
        )
        client = asana.Client.access_token(access_token)

        # Find 'Delivered' section
        sections = client.sections.find_by_project(project_gid)
        delivered_section = next(
            (s for s in sections if s["name"].lower() == "delivered"), None
        )

        if not delivered_section:
            return {"labels": [], "datasets": [{"data": []}]}

        # Get completed tasks from delivered section
        tasks = client.tasks.find_by_section(
            delivered_section["gid"], {"opt_fields": "completed_at"}
        )

        # Group by month
        months = [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "May",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Oct",
            "Nov",
            "Dec",
        ]
        monthly_counts = {}

        # Initialize past 6 months
        now = datetime.now()
        for i in range(5, -1, -1):
            month_date = now - timedelta(days=30 * i)
            month_key = f"{months[month_date.month - 1]} {month_date.year}"
            monthly_counts[month_key] = 0

        # Count tasks by completion month
        for task in tasks:
            if task.get("completed_at"):
                completed_date = datetime.fromisoformat(
                    task["completed_at"].replace("Z", "+00:00")
                )
                month_key = f"{months[completed_date.month - 1]} {completed_date.year}"

                if month_key in monthly_counts:
                    monthly_counts[month_key] += 1

        # Format data for chart
        chart_data = {
            "labels": list(monthly_counts.keys()),
            "datasets": [
                {
                    "label": "Delivered",
                    "data": list(monthly_counts.values()),
                    "borderColor": "#3B82F6",
                    "backgroundColor": "rgba(59, 130, 246, 0.1)",
                    "tension": 0.4,
                },
                {
                    "label": "Expected",
                    "data": [
                        count + round(count * 0.2) for count in monthly_counts.values()
                    ],
                    "borderColor": "#E5E7EB",
                    "borderDash": [5, 5],
                    "backgroundColor": "transparent",
                    "tension": 0.4,
                },
            ],
        }

        return chart_data

    except Exception as e:
        app.logger.error(f"Error getting monthly deliveries: {str(e)}")
        return {"labels": [], "datasets": [{"data": []}]}


def get_recent_shipments_from_asana(project_gid, limit=5):
    """Get recent shipments from Asana for table display"""
    try:
        # Initialize Asana API client
        access_token = os.environ.get(
            "ASANA_ACCESS_TOKEN", "***************************************************"
        )
        client = asana.Client.access_token(access_token)

        # Get all sections
        sections = client.sections.find_by_project(project_gid)

        # Get tasks from all sections
        all_tasks = []
        for section in sections:
            tasks = client.tasks.find_by_section(
                section["gid"],
                {"opt_fields": "name,created_at,custom_fields,completed"},
            )

            # Add section name to each task
            for task in tasks:
                task["section_name"] = section["name"]
                all_tasks.append(task)

        # Sort by creation date (newest first)
        all_tasks.sort(key=lambda x: x.get("created_at", ""), reverse=True)

        # Get the most recent tasks
        recent_tasks = all_tasks[:limit]

        # Extract wafer count from custom fields
        shipments = []
        for task in recent_tasks:
            wafer_count = 0
            lot_id = ""

            # Extract custom fields
            if "custom_fields" in task:
                for field in task["custom_fields"]:
                    if (
                        field["name"].lower().find("wafer") >= 0
                        and field.get("number_value") is not None
                    ):
                        wafer_count = int(field["number_value"])
                    if field["name"].lower() == "lgt lot id" and field.get(
                        "text_value"
                    ):
                        lot_id = field["text_value"]

            created_date = datetime.fromisoformat(
                task.get("created_at", "").replace("Z", "+00:00")
            )

            shipments.append(
                {
                    "id": f"#SH-{created_date.year}-{task['gid'][-3:]}",
                    "name": task.get("name", "Unnamed Task"),
                    "lot": lot_id or f"LOT-{hash(task['gid']) % 100000:05d}",
                    "wafers": wafer_count,
                    "status": task.get("section_name", "Unknown"),
                    "date": created_date.strftime("%b %d, %Y"),
                    "task_id": task["gid"],
                }
            )

        return shipments

    except Exception as e:
        app.logger.error(f"Error getting recent shipments: {str(e)}")
        return []


def init_db():
    """Initialize database with schema"""
    try:
        conn = get_db_connection()
        cur = conn.cursor()

        # Read schema.sql file
        schema_path = os.path.join(os.path.dirname(__file__), "database", "schema.sql")
        with open(schema_path, "r") as f:
            cur.execute(f.read())

        conn.commit()
        cur.close()
        conn.close()
        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Error initializing database: {str(e)}")
        raise


if __name__ == "__main__":
    # Get configuration from environment variables
    import socket
    import sys

    # Try to find an available port
    def is_port_in_use(port):
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            return s.connect_ex(("localhost", port)) == 0

    # Try different ports in sequence
    port_options = [3000, 3500, 4000, 4500, 5500, 6000, 7000, 8888, 9000]
    port = 5000  # Start with default port

    # Check if a port was specified in the command line
    if "--port" in sys.argv:
        try:
            port_index = sys.argv.index("--port") + 1
            if port_index < len(sys.argv):
                port = int(sys.argv[port_index])
        except (ValueError, IndexError):
            pass

    # If the default port is in use, try the alternatives
    if is_port_in_use(port):
        logger.warning(f"Port {port} is already in use. Trying alternative ports...")
        for alt_port in port_options:
            if not is_port_in_use(alt_port):
                port = alt_port
                logger.info(f"Found available port: {port}")
                break
        else:
            logger.warning(
                "Could not find an available port in the preferred range. Using a random high port."
            )
            # Let the OS pick a random high port
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("", 0))
                port = s.getsockname()[1]

    # Enable debug mode in development
    debug = os.getenv("FLASK_DEBUG") == "1" or os.getenv("FLASK_ENV") == "development"
    use_reloader = debug  # Auto-reload in debug mode

    # Print startup info
    logger.info(f"Starting Talaria Dashboard on port {port}")
    logger.info(f"Debug mode: {debug}, Auto-reload: {use_reloader}")

    try:
        # Initialize database if needed
        if os.getenv("INIT_DB", "False").lower() == "true":
            init_db()

        # Start the SocketIO server with auto-reloader for development
        socketio.run(
            app,
            host="0.0.0.0",
            port=port,
            debug=debug,
            use_reloader=use_reloader,
            allow_unsafe_werkzeug=True,
            extra_files=[  # Monitor additional files for changes
                "./static/js/inventory_management.js",
                "./templates/inventory_management.html",
                "./routes/inventory_routes.py",
                "./routes/helpers/inventory_metadata_helpers.py",
            ],
        )
    except Exception as e:
        logger.critical(f"Failed to start application: {str(e)}")
        logger.critical(traceback.format_exc())
