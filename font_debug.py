import os
import subprocess
import sys
from PIL import ImageFont, Image, ImageDraw


def check_font_packages():
    """Check installed font packages"""
    print("=== Checking installed font packages ===")
    try:
        result = subprocess.run(["apk", "info"], capture_output=True, text=True)
        font_packages = [
            pkg for pkg in result.stdout.split() if "font" in pkg or "ttf" in pkg
        ]
        print(f"Found font packages: {font_packages}")
    except Exception as e:
        print(f"Error checking font packages: {e}")


def find_font_files():
    """Find font files in common directories"""
    print("\n=== Finding font files ===")
    font_dirs = ["/usr/share/fonts", "/usr/local/share/fonts", "/home/<USER>/.fonts"]

    for base_dir in font_dirs:
        if not os.path.exists(base_dir):
            print(f"Directory does not exist: {base_dir}")
            continue

        print(f"Scanning {base_dir}...")
        for root, dirs, files in os.walk(base_dir):
            font_files = [f for f in files if f.endswith((".ttf", ".otf"))]
            if font_files:
                print(f"  {root}: {font_files}")


def check_font_config():
    """Check font configuration"""
    print("\n=== Checking font configuration ===")
    try:
        result = subprocess.run(["fc-list"], capture_output=True, text=True)
        print("Font configuration list:")
        fonts = result.stdout.split("\n")
        for i, font in enumerate(fonts[:20]):  # Show first 20 fonts
            if font.strip():
                print(f"  {i+1}. {font}")

        if len(fonts) > 20:
            print(f"  ... and {len(fonts) - 20} more fonts")

        # Specifically look for DejaVu Sans
        dejavu_fonts = [f for f in fonts if "dejavu" in f.lower()]
        print("\nDejaVu fonts:")
        for font in dejavu_fonts:
            print(f"  {font}")

    except Exception as e:
        print(f"Error checking font configuration: {e}")


def test_pil_fonts():
    """Test PIL font loading"""
    print("\n=== Testing PIL font loading ===")

    test_fonts = [
        ("/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf", "DejaVu Sans (ttf-dejavu)"),
        (
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "DejaVu Sans (truetype/dejavu)",
        ),
        ("/usr/share/fonts/truetype/custom/Arial.ttf", "Custom Arial mapping"),
    ]

    for path, name in test_fonts:
        try:
            if os.path.exists(path):
                font = ImageFont.truetype(path, 12)
                print(f"✅ Successfully loaded {name} from {path}")
                # Create a test image to check if font works
                img = Image.new("RGB", (400, 30), color="white")
                draw = ImageDraw.Draw(img)
                draw.text((10, 10), f"Testing font: {name}", fill=(0, 0, 0), font=font)
                print(f"   Font loads and renders correctly")
            else:
                print(f"❌ Font file not found: {path}")
        except Exception as e:
            print(f"❌ Error loading {name}: {e}")

    # Test default font
    try:
        default_font = ImageFont.load_default()
        print(f"✅ Successfully loaded default PIL font")
    except Exception as e:
        print(f"❌ Error loading default font: {e}")


def test_reportlab_fonts():
    """Test ReportLab font registration and metrics"""
    print("\n=== Testing ReportLab font handling ===")

    try:
        from reportlab.pdfbase import pdfmetrics
        from reportlab.pdfbase.ttfonts import TTFont
        from reportlab.pdfgen import canvas
        import tempfile

        print("Checking registered fonts:")
        registered_fonts = pdfmetrics.getRegisteredFontNames()
        print(f"  Registered fonts: {', '.join(registered_fonts[:10])}...")

        # Test registering DejaVu Sans
        dejavu_paths = [
            "/usr/share/fonts/ttf-dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/custom/Arial.ttf",
        ]

        for path in dejavu_paths:
            if os.path.exists(path):
                font_name = os.path.basename(path).split(".")[0]
                try:
                    if font_name not in registered_fonts:
                        pdfmetrics.registerFont(TTFont(font_name, path))
                        print(f"✅ Successfully registered {font_name} from {path}")
                    else:
                        print(f"✅ Font {font_name} already registered")

                    # Test string width - this is what fails in the app
                    width = pdfmetrics.stringWidth("Test text", font_name, 12)
                    print(
                        f"   String width measurement works: 'Test text' is {width} points wide"
                    )

                    # Test PDF generation
                    with tempfile.NamedTemporaryFile(
                        suffix=".pdf", delete=False
                    ) as tmp:
                        c = canvas.Canvas(tmp.name)
                        c.setFont(font_name, 12)
                        c.drawString(100, 100, f"Testing {font_name}")
                        c.save()
                        print(f"   PDF generation with {font_name} works")

                except Exception as e:
                    print(f"❌ Error with {font_name}: {e}")
            else:
                print(f"❌ Font file not found: {path}")

    except ImportError:
        print("ReportLab not installed")
    except Exception as e:
        print(f"Error in ReportLab font testing: {e}")


if __name__ == "__main__":
    print(f"Python version: {sys.version}")
    print(f"PIL version: {Image.__version__}")

    check_font_packages()
    find_font_files()
    check_font_config()
    test_pil_fonts()
    test_reportlab_fonts()

    print("\nFont debugging complete")
